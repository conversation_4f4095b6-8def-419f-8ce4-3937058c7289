(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function bc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Pu={exports:{}},xi={},Lu={exports:{}},O={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ur=Symbol.for("react.element"),ef=Symbol.for("react.portal"),tf=Symbol.for("react.fragment"),nf=Symbol.for("react.strict_mode"),rf=Symbol.for("react.profiler"),sf=Symbol.for("react.provider"),of=Symbol.for("react.context"),lf=Symbol.for("react.forward_ref"),uf=Symbol.for("react.suspense"),af=Symbol.for("react.memo"),cf=Symbol.for("react.lazy"),pl=Symbol.iterator;function ff(e){return e===null||typeof e!="object"?null:(e=pl&&e[pl]||e["@@iterator"],typeof e=="function"?e:null)}var Ru={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ju=Object.assign,Ou={};function vn(e,t,n){this.props=e,this.context=t,this.refs=Ou,this.updater=n||Ru}vn.prototype.isReactComponent={};vn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};vn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Du(){}Du.prototype=vn.prototype;function ho(e,t,n){this.props=e,this.context=t,this.refs=Ou,this.updater=n||Ru}var mo=ho.prototype=new Du;mo.constructor=ho;ju(mo,vn.prototype);mo.isPureReactComponent=!0;var hl=Array.isArray,zu=Object.prototype.hasOwnProperty,yo={current:null},Mu={key:!0,ref:!0,__self:!0,__source:!0};function Au(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)zu.call(t,r)&&!Mu.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var u=Array(l),d=0;d<l;d++)u[d]=arguments[d+2];i.children=u}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:ur,type:e,key:s,ref:o,props:i,_owner:yo.current}}function df(e,t){return{$$typeof:ur,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function go(e){return typeof e=="object"&&e!==null&&e.$$typeof===ur}function pf(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ml=/\/+/g;function Fi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?pf(""+e.key):t.toString(36)}function zr(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ur:case ef:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Fi(o,0):r,hl(i)?(n="",e!=null&&(n=e.replace(ml,"$&/")+"/"),zr(i,t,n,"",function(d){return d})):i!=null&&(go(i)&&(i=df(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(ml,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",hl(e))for(var l=0;l<e.length;l++){s=e[l];var u=r+Fi(s,l);o+=zr(s,t,n,u,i)}else if(u=ff(e),typeof u=="function")for(e=u.call(e),l=0;!(s=e.next()).done;)s=s.value,u=r+Fi(s,l++),o+=zr(s,t,n,u,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function mr(e,t,n){if(e==null)return e;var r=[],i=0;return zr(e,r,"","",function(s){return t.call(n,s,i++)}),r}function hf(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var fe={current:null},Mr={transition:null},mf={ReactCurrentDispatcher:fe,ReactCurrentBatchConfig:Mr,ReactCurrentOwner:yo};function Iu(){throw Error("act(...) is not supported in production builds of React.")}O.Children={map:mr,forEach:function(e,t,n){mr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return mr(e,function(){t++}),t},toArray:function(e){return mr(e,function(t){return t})||[]},only:function(e){if(!go(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};O.Component=vn;O.Fragment=tf;O.Profiler=rf;O.PureComponent=ho;O.StrictMode=nf;O.Suspense=uf;O.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=mf;O.act=Iu;O.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ju({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=yo.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)zu.call(t,u)&&!Mu.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&l!==void 0?l[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){l=Array(u);for(var d=0;d<u;d++)l[d]=arguments[d+2];r.children=l}return{$$typeof:ur,type:e.type,key:i,ref:s,props:r,_owner:o}};O.createContext=function(e){return e={$$typeof:of,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:sf,_context:e},e.Consumer=e};O.createElement=Au;O.createFactory=function(e){var t=Au.bind(null,e);return t.type=e,t};O.createRef=function(){return{current:null}};O.forwardRef=function(e){return{$$typeof:lf,render:e}};O.isValidElement=go;O.lazy=function(e){return{$$typeof:cf,_payload:{_status:-1,_result:e},_init:hf}};O.memo=function(e,t){return{$$typeof:af,type:e,compare:t===void 0?null:t}};O.startTransition=function(e){var t=Mr.transition;Mr.transition={};try{e()}finally{Mr.transition=t}};O.unstable_act=Iu;O.useCallback=function(e,t){return fe.current.useCallback(e,t)};O.useContext=function(e){return fe.current.useContext(e)};O.useDebugValue=function(){};O.useDeferredValue=function(e){return fe.current.useDeferredValue(e)};O.useEffect=function(e,t){return fe.current.useEffect(e,t)};O.useId=function(){return fe.current.useId()};O.useImperativeHandle=function(e,t,n){return fe.current.useImperativeHandle(e,t,n)};O.useInsertionEffect=function(e,t){return fe.current.useInsertionEffect(e,t)};O.useLayoutEffect=function(e,t){return fe.current.useLayoutEffect(e,t)};O.useMemo=function(e,t){return fe.current.useMemo(e,t)};O.useReducer=function(e,t,n){return fe.current.useReducer(e,t,n)};O.useRef=function(e){return fe.current.useRef(e)};O.useState=function(e){return fe.current.useState(e)};O.useSyncExternalStore=function(e,t,n){return fe.current.useSyncExternalStore(e,t,n)};O.useTransition=function(){return fe.current.useTransition()};O.version="18.3.1";Lu.exports=O;var ne=Lu.exports;const yf=bc(ne);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gf=ne,vf=Symbol.for("react.element"),wf=Symbol.for("react.fragment"),xf=Object.prototype.hasOwnProperty,kf=gf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Sf={key:!0,ref:!0,__self:!0,__source:!0};function Bu(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)xf.call(t,r)&&!Sf.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:vf,type:e,key:s,ref:o,props:i,_owner:kf.current}}xi.Fragment=wf;xi.jsx=Bu;xi.jsxs=Bu;Pu.exports=xi;var h=Pu.exports,hs={},Fu={exports:{}},_e={},Uu={exports:{}},$u={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,L){var R=T.length;T.push(L);e:for(;0<R;){var K=R-1>>>1,Z=T[K];if(0<i(Z,L))T[K]=L,T[R]=Z,R=K;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var L=T[0],R=T.pop();if(R!==L){T[0]=R;e:for(var K=0,Z=T.length,pr=Z>>>1;K<pr;){var Pt=2*(K+1)-1,Bi=T[Pt],Lt=Pt+1,hr=T[Lt];if(0>i(Bi,R))Lt<Z&&0>i(hr,Bi)?(T[K]=hr,T[Lt]=R,K=Lt):(T[K]=Bi,T[Pt]=R,K=Pt);else if(Lt<Z&&0>i(hr,R))T[K]=hr,T[Lt]=R,K=Lt;else break e}}return L}function i(T,L){var R=T.sortIndex-L.sortIndex;return R!==0?R:T.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var u=[],d=[],g=1,y=null,m=3,S=!1,E=!1,N=!1,M=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,a=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(T){for(var L=n(d);L!==null;){if(L.callback===null)r(d);else if(L.startTime<=T)r(d),L.sortIndex=L.expirationTime,t(u,L);else break;L=n(d)}}function c(T){if(N=!1,f(T),!E)if(n(u)!==null)E=!0,pe(v);else{var L=n(d);L!==null&&Wt(c,L.startTime-T)}}function v(T,L){E=!1,N&&(N=!1,p(_),_=-1),S=!0;var R=m;try{for(f(L),y=n(u);y!==null&&(!(y.expirationTime>L)||T&&!$());){var K=y.callback;if(typeof K=="function"){y.callback=null,m=y.priorityLevel;var Z=K(y.expirationTime<=L);L=e.unstable_now(),typeof Z=="function"?y.callback=Z:y===n(u)&&r(u),f(L)}else r(u);y=n(u)}if(y!==null)var pr=!0;else{var Pt=n(d);Pt!==null&&Wt(c,Pt.startTime-L),pr=!1}return pr}finally{y=null,m=R,S=!1}}var x=!1,k=null,_=-1,j=5,P=-1;function $(){return!(e.unstable_now()-P<j)}function Ct(){if(k!==null){var T=e.unstable_now();P=T;var L=!0;try{L=k(!0,T)}finally{L?Tt():(x=!1,k=null)}}else x=!1}var Tt;if(typeof a=="function")Tt=function(){a(Ct)};else if(typeof MessageChannel<"u"){var it=new MessageChannel,Ue=it.port2;it.port1.onmessage=Ct,Tt=function(){Ue.postMessage(null)}}else Tt=function(){M(Ct,0)};function pe(T){k=T,x||(x=!0,Tt())}function Wt(T,L){_=M(function(){T(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){E||S||(E=!0,pe(v))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(T){switch(m){case 1:case 2:case 3:var L=3;break;default:L=m}var R=m;m=L;try{return T()}finally{m=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,L){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var R=m;m=T;try{return L()}finally{m=R}},e.unstable_scheduleCallback=function(T,L,R){var K=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?K+R:K):R=K,T){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=**********;break;case 4:Z=1e4;break;default:Z=5e3}return Z=R+Z,T={id:g++,callback:L,priorityLevel:T,startTime:R,expirationTime:Z,sortIndex:-1},R>K?(T.sortIndex=R,t(d,T),n(u)===null&&T===n(d)&&(N?(p(_),_=-1):N=!0,Wt(c,R-K))):(T.sortIndex=Z,t(u,T),E||S||(E=!0,pe(v))),T},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(T){var L=m;return function(){var R=m;m=L;try{return T.apply(this,arguments)}finally{m=R}}}})($u);Uu.exports=$u;var _f=Uu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ef=ne,Se=_f;function w(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Vu=new Set,Qn={};function Vt(e,t){fn(e,t),fn(e+"Capture",t)}function fn(e,t){for(Qn[e]=t,e=0;e<t.length;e++)Vu.add(t[e])}var be=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ms=Object.prototype.hasOwnProperty,Nf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,yl={},gl={};function Cf(e){return ms.call(gl,e)?!0:ms.call(yl,e)?!1:Nf.test(e)?gl[e]=!0:(yl[e]=!0,!1)}function Tf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Pf(e,t,n,r){if(t===null||typeof t>"u"||Tf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function de(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var ie={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ie[e]=new de(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ie[t]=new de(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ie[e]=new de(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ie[e]=new de(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ie[e]=new de(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ie[e]=new de(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ie[e]=new de(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ie[e]=new de(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ie[e]=new de(e,5,!1,e.toLowerCase(),null,!1,!1)});var vo=/[\-:]([a-z])/g;function wo(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(vo,wo);ie[t]=new de(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(vo,wo);ie[t]=new de(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(vo,wo);ie[t]=new de(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ie[e]=new de(e,1,!1,e.toLowerCase(),null,!1,!1)});ie.xlinkHref=new de("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ie[e]=new de(e,1,!1,e.toLowerCase(),null,!0,!0)});function xo(e,t,n,r){var i=ie.hasOwnProperty(t)?ie[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Pf(t,n,i,r)&&(n=null),r||i===null?Cf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var rt=Ef.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,yr=Symbol.for("react.element"),Kt=Symbol.for("react.portal"),Yt=Symbol.for("react.fragment"),ko=Symbol.for("react.strict_mode"),ys=Symbol.for("react.profiler"),Hu=Symbol.for("react.provider"),Wu=Symbol.for("react.context"),So=Symbol.for("react.forward_ref"),gs=Symbol.for("react.suspense"),vs=Symbol.for("react.suspense_list"),_o=Symbol.for("react.memo"),ot=Symbol.for("react.lazy"),Qu=Symbol.for("react.offscreen"),vl=Symbol.iterator;function Sn(e){return e===null||typeof e!="object"?null:(e=vl&&e[vl]||e["@@iterator"],typeof e=="function"?e:null)}var W=Object.assign,Ui;function jn(e){if(Ui===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ui=t&&t[1]||""}return`
`+Ui+e}var $i=!1;function Vi(e,t){if(!e||$i)return"";$i=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(d){var r=d}Reflect.construct(e,[],t)}else{try{t.call()}catch(d){r=d}e.call(t.prototype)}else{try{throw Error()}catch(d){r=d}e()}}catch(d){if(d&&r&&typeof d.stack=="string"){for(var i=d.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,l=s.length-1;1<=o&&0<=l&&i[o]!==s[l];)l--;for(;1<=o&&0<=l;o--,l--)if(i[o]!==s[l]){if(o!==1||l!==1)do if(o--,l--,0>l||i[o]!==s[l]){var u=`
`+i[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=l);break}}}finally{$i=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?jn(e):""}function Lf(e){switch(e.tag){case 5:return jn(e.type);case 16:return jn("Lazy");case 13:return jn("Suspense");case 19:return jn("SuspenseList");case 0:case 2:case 15:return e=Vi(e.type,!1),e;case 11:return e=Vi(e.type.render,!1),e;case 1:return e=Vi(e.type,!0),e;default:return""}}function ws(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Yt:return"Fragment";case Kt:return"Portal";case ys:return"Profiler";case ko:return"StrictMode";case gs:return"Suspense";case vs:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Wu:return(e.displayName||"Context")+".Consumer";case Hu:return(e._context.displayName||"Context")+".Provider";case So:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case _o:return t=e.displayName||null,t!==null?t:ws(e.type)||"Memo";case ot:t=e._payload,e=e._init;try{return ws(e(t))}catch{}}return null}function Rf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ws(t);case 8:return t===ko?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function kt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ku(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function jf(e){var t=Ku(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function gr(e){e._valueTracker||(e._valueTracker=jf(e))}function Yu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ku(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Gr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function xs(e,t){var n=t.checked;return W({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function wl(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=kt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function qu(e,t){t=t.checked,t!=null&&xo(e,"checked",t,!1)}function ks(e,t){qu(e,t);var n=kt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ss(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ss(e,t.type,kt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function xl(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ss(e,t,n){(t!=="number"||Gr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var On=Array.isArray;function sn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+kt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function _s(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(w(91));return W({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function kl(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(w(92));if(On(n)){if(1<n.length)throw Error(w(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:kt(n)}}function Xu(e,t){var n=kt(t.value),r=kt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Sl(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Gu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Es(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Gu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var vr,Ju=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(vr=vr||document.createElement("div"),vr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=vr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Kn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var An={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Of=["Webkit","ms","Moz","O"];Object.keys(An).forEach(function(e){Of.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),An[t]=An[e]})});function Zu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||An.hasOwnProperty(e)&&An[e]?(""+t).trim():t+"px"}function bu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Zu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Df=W({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ns(e,t){if(t){if(Df[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(w(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(w(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(w(61))}if(t.style!=null&&typeof t.style!="object")throw Error(w(62))}}function Cs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ts=null;function Eo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ps=null,on=null,ln=null;function _l(e){if(e=fr(e)){if(typeof Ps!="function")throw Error(w(280));var t=e.stateNode;t&&(t=Ni(t),Ps(e.stateNode,e.type,t))}}function ea(e){on?ln?ln.push(e):ln=[e]:on=e}function ta(){if(on){var e=on,t=ln;if(ln=on=null,_l(e),t)for(e=0;e<t.length;e++)_l(t[e])}}function na(e,t){return e(t)}function ra(){}var Hi=!1;function ia(e,t,n){if(Hi)return e(t,n);Hi=!0;try{return na(e,t,n)}finally{Hi=!1,(on!==null||ln!==null)&&(ra(),ta())}}function Yn(e,t){var n=e.stateNode;if(n===null)return null;var r=Ni(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(w(231,t,typeof n));return n}var Ls=!1;if(be)try{var _n={};Object.defineProperty(_n,"passive",{get:function(){Ls=!0}}),window.addEventListener("test",_n,_n),window.removeEventListener("test",_n,_n)}catch{Ls=!1}function zf(e,t,n,r,i,s,o,l,u){var d=Array.prototype.slice.call(arguments,3);try{t.apply(n,d)}catch(g){this.onError(g)}}var In=!1,Jr=null,Zr=!1,Rs=null,Mf={onError:function(e){In=!0,Jr=e}};function Af(e,t,n,r,i,s,o,l,u){In=!1,Jr=null,zf.apply(Mf,arguments)}function If(e,t,n,r,i,s,o,l,u){if(Af.apply(this,arguments),In){if(In){var d=Jr;In=!1,Jr=null}else throw Error(w(198));Zr||(Zr=!0,Rs=d)}}function Ht(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function sa(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function El(e){if(Ht(e)!==e)throw Error(w(188))}function Bf(e){var t=e.alternate;if(!t){if(t=Ht(e),t===null)throw Error(w(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return El(i),e;if(s===r)return El(i),t;s=s.sibling}throw Error(w(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(w(189))}}if(n.alternate!==r)throw Error(w(190))}if(n.tag!==3)throw Error(w(188));return n.stateNode.current===n?e:t}function oa(e){return e=Bf(e),e!==null?la(e):null}function la(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=la(e);if(t!==null)return t;e=e.sibling}return null}var ua=Se.unstable_scheduleCallback,Nl=Se.unstable_cancelCallback,Ff=Se.unstable_shouldYield,Uf=Se.unstable_requestPaint,Y=Se.unstable_now,$f=Se.unstable_getCurrentPriorityLevel,No=Se.unstable_ImmediatePriority,aa=Se.unstable_UserBlockingPriority,br=Se.unstable_NormalPriority,Vf=Se.unstable_LowPriority,ca=Se.unstable_IdlePriority,ki=null,We=null;function Hf(e){if(We&&typeof We.onCommitFiberRoot=="function")try{We.onCommitFiberRoot(ki,e,void 0,(e.current.flags&128)===128)}catch{}}var Ie=Math.clz32?Math.clz32:Kf,Wf=Math.log,Qf=Math.LN2;function Kf(e){return e>>>=0,e===0?32:31-(Wf(e)/Qf|0)|0}var wr=64,xr=4194304;function Dn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ei(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~i;l!==0?r=Dn(l):(s&=o,s!==0&&(r=Dn(s)))}else o=n&~i,o!==0?r=Dn(o):s!==0&&(r=Dn(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ie(t),i=1<<n,r|=e[n],t&=~i;return r}function Yf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function qf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-Ie(s),l=1<<o,u=i[o];u===-1?(!(l&n)||l&r)&&(i[o]=Yf(l,t)):u<=t&&(e.expiredLanes|=l),s&=~l}}function js(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function fa(){var e=wr;return wr<<=1,!(wr&4194240)&&(wr=64),e}function Wi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ar(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ie(t),e[t]=n}function Xf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ie(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function Co(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ie(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var A=0;function da(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var pa,To,ha,ma,ya,Os=!1,kr=[],dt=null,pt=null,ht=null,qn=new Map,Xn=new Map,ut=[],Gf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Cl(e,t){switch(e){case"focusin":case"focusout":dt=null;break;case"dragenter":case"dragleave":pt=null;break;case"mouseover":case"mouseout":ht=null;break;case"pointerover":case"pointerout":qn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Xn.delete(t.pointerId)}}function En(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=fr(t),t!==null&&To(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Jf(e,t,n,r,i){switch(t){case"focusin":return dt=En(dt,e,t,n,r,i),!0;case"dragenter":return pt=En(pt,e,t,n,r,i),!0;case"mouseover":return ht=En(ht,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return qn.set(s,En(qn.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Xn.set(s,En(Xn.get(s)||null,e,t,n,r,i)),!0}return!1}function ga(e){var t=Ot(e.target);if(t!==null){var n=Ht(t);if(n!==null){if(t=n.tag,t===13){if(t=sa(n),t!==null){e.blockedOn=t,ya(e.priority,function(){ha(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ar(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ds(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ts=r,n.target.dispatchEvent(r),Ts=null}else return t=fr(n),t!==null&&To(t),e.blockedOn=n,!1;t.shift()}return!0}function Tl(e,t,n){Ar(e)&&n.delete(t)}function Zf(){Os=!1,dt!==null&&Ar(dt)&&(dt=null),pt!==null&&Ar(pt)&&(pt=null),ht!==null&&Ar(ht)&&(ht=null),qn.forEach(Tl),Xn.forEach(Tl)}function Nn(e,t){e.blockedOn===t&&(e.blockedOn=null,Os||(Os=!0,Se.unstable_scheduleCallback(Se.unstable_NormalPriority,Zf)))}function Gn(e){function t(i){return Nn(i,e)}if(0<kr.length){Nn(kr[0],e);for(var n=1;n<kr.length;n++){var r=kr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(dt!==null&&Nn(dt,e),pt!==null&&Nn(pt,e),ht!==null&&Nn(ht,e),qn.forEach(t),Xn.forEach(t),n=0;n<ut.length;n++)r=ut[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ut.length&&(n=ut[0],n.blockedOn===null);)ga(n),n.blockedOn===null&&ut.shift()}var un=rt.ReactCurrentBatchConfig,ti=!0;function bf(e,t,n,r){var i=A,s=un.transition;un.transition=null;try{A=1,Po(e,t,n,r)}finally{A=i,un.transition=s}}function ed(e,t,n,r){var i=A,s=un.transition;un.transition=null;try{A=4,Po(e,t,n,r)}finally{A=i,un.transition=s}}function Po(e,t,n,r){if(ti){var i=Ds(e,t,n,r);if(i===null)es(e,t,r,ni,n),Cl(e,r);else if(Jf(i,e,t,n,r))r.stopPropagation();else if(Cl(e,r),t&4&&-1<Gf.indexOf(e)){for(;i!==null;){var s=fr(i);if(s!==null&&pa(s),s=Ds(e,t,n,r),s===null&&es(e,t,r,ni,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else es(e,t,r,null,n)}}var ni=null;function Ds(e,t,n,r){if(ni=null,e=Eo(r),e=Ot(e),e!==null)if(t=Ht(e),t===null)e=null;else if(n=t.tag,n===13){if(e=sa(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ni=e,null}function va(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch($f()){case No:return 1;case aa:return 4;case br:case Vf:return 16;case ca:return 536870912;default:return 16}default:return 16}}var ct=null,Lo=null,Ir=null;function wa(){if(Ir)return Ir;var e,t=Lo,n=t.length,r,i="value"in ct?ct.value:ct.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return Ir=i.slice(e,1<r?1-r:void 0)}function Br(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Sr(){return!0}function Pl(){return!1}function Ee(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Sr:Pl,this.isPropagationStopped=Pl,this}return W(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Sr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Sr)},persist:function(){},isPersistent:Sr}),t}var wn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ro=Ee(wn),cr=W({},wn,{view:0,detail:0}),td=Ee(cr),Qi,Ki,Cn,Si=W({},cr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Cn&&(Cn&&e.type==="mousemove"?(Qi=e.screenX-Cn.screenX,Ki=e.screenY-Cn.screenY):Ki=Qi=0,Cn=e),Qi)},movementY:function(e){return"movementY"in e?e.movementY:Ki}}),Ll=Ee(Si),nd=W({},Si,{dataTransfer:0}),rd=Ee(nd),id=W({},cr,{relatedTarget:0}),Yi=Ee(id),sd=W({},wn,{animationName:0,elapsedTime:0,pseudoElement:0}),od=Ee(sd),ld=W({},wn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ud=Ee(ld),ad=W({},wn,{data:0}),Rl=Ee(ad),cd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},fd={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dd={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=dd[e])?!!t[e]:!1}function jo(){return pd}var hd=W({},cr,{key:function(e){if(e.key){var t=cd[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Br(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?fd[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jo,charCode:function(e){return e.type==="keypress"?Br(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Br(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),md=Ee(hd),yd=W({},Si,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),jl=Ee(yd),gd=W({},cr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jo}),vd=Ee(gd),wd=W({},wn,{propertyName:0,elapsedTime:0,pseudoElement:0}),xd=Ee(wd),kd=W({},Si,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Sd=Ee(kd),_d=[9,13,27,32],Oo=be&&"CompositionEvent"in window,Bn=null;be&&"documentMode"in document&&(Bn=document.documentMode);var Ed=be&&"TextEvent"in window&&!Bn,xa=be&&(!Oo||Bn&&8<Bn&&11>=Bn),Ol=" ",Dl=!1;function ka(e,t){switch(e){case"keyup":return _d.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Sa(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var qt=!1;function Nd(e,t){switch(e){case"compositionend":return Sa(t);case"keypress":return t.which!==32?null:(Dl=!0,Ol);case"textInput":return e=t.data,e===Ol&&Dl?null:e;default:return null}}function Cd(e,t){if(qt)return e==="compositionend"||!Oo&&ka(e,t)?(e=wa(),Ir=Lo=ct=null,qt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return xa&&t.locale!=="ko"?null:t.data;default:return null}}var Td={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Td[e.type]:t==="textarea"}function _a(e,t,n,r){ea(r),t=ri(t,"onChange"),0<t.length&&(n=new Ro("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Fn=null,Jn=null;function Pd(e){za(e,0)}function _i(e){var t=Jt(e);if(Yu(t))return e}function Ld(e,t){if(e==="change")return t}var Ea=!1;if(be){var qi;if(be){var Xi="oninput"in document;if(!Xi){var Ml=document.createElement("div");Ml.setAttribute("oninput","return;"),Xi=typeof Ml.oninput=="function"}qi=Xi}else qi=!1;Ea=qi&&(!document.documentMode||9<document.documentMode)}function Al(){Fn&&(Fn.detachEvent("onpropertychange",Na),Jn=Fn=null)}function Na(e){if(e.propertyName==="value"&&_i(Jn)){var t=[];_a(t,Jn,e,Eo(e)),ia(Pd,t)}}function Rd(e,t,n){e==="focusin"?(Al(),Fn=t,Jn=n,Fn.attachEvent("onpropertychange",Na)):e==="focusout"&&Al()}function jd(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return _i(Jn)}function Od(e,t){if(e==="click")return _i(t)}function Dd(e,t){if(e==="input"||e==="change")return _i(t)}function zd(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Fe=typeof Object.is=="function"?Object.is:zd;function Zn(e,t){if(Fe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!ms.call(t,i)||!Fe(e[i],t[i]))return!1}return!0}function Il(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Bl(e,t){var n=Il(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Il(n)}}function Ca(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ca(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ta(){for(var e=window,t=Gr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Gr(e.document)}return t}function Do(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Md(e){var t=Ta(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ca(n.ownerDocument.documentElement,n)){if(r!==null&&Do(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Bl(n,s);var o=Bl(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ad=be&&"documentMode"in document&&11>=document.documentMode,Xt=null,zs=null,Un=null,Ms=!1;function Fl(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ms||Xt==null||Xt!==Gr(r)||(r=Xt,"selectionStart"in r&&Do(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Un&&Zn(Un,r)||(Un=r,r=ri(zs,"onSelect"),0<r.length&&(t=new Ro("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Xt)))}function _r(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Gt={animationend:_r("Animation","AnimationEnd"),animationiteration:_r("Animation","AnimationIteration"),animationstart:_r("Animation","AnimationStart"),transitionend:_r("Transition","TransitionEnd")},Gi={},Pa={};be&&(Pa=document.createElement("div").style,"AnimationEvent"in window||(delete Gt.animationend.animation,delete Gt.animationiteration.animation,delete Gt.animationstart.animation),"TransitionEvent"in window||delete Gt.transitionend.transition);function Ei(e){if(Gi[e])return Gi[e];if(!Gt[e])return e;var t=Gt[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Pa)return Gi[e]=t[n];return e}var La=Ei("animationend"),Ra=Ei("animationiteration"),ja=Ei("animationstart"),Oa=Ei("transitionend"),Da=new Map,Ul="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _t(e,t){Da.set(e,t),Vt(t,[e])}for(var Ji=0;Ji<Ul.length;Ji++){var Zi=Ul[Ji],Id=Zi.toLowerCase(),Bd=Zi[0].toUpperCase()+Zi.slice(1);_t(Id,"on"+Bd)}_t(La,"onAnimationEnd");_t(Ra,"onAnimationIteration");_t(ja,"onAnimationStart");_t("dblclick","onDoubleClick");_t("focusin","onFocus");_t("focusout","onBlur");_t(Oa,"onTransitionEnd");fn("onMouseEnter",["mouseout","mouseover"]);fn("onMouseLeave",["mouseout","mouseover"]);fn("onPointerEnter",["pointerout","pointerover"]);fn("onPointerLeave",["pointerout","pointerover"]);Vt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Vt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Vt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Vt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Vt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Vt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fd=new Set("cancel close invalid load scroll toggle".split(" ").concat(zn));function $l(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,If(r,t,void 0,e),e.currentTarget=null}function za(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],u=l.instance,d=l.currentTarget;if(l=l.listener,u!==s&&i.isPropagationStopped())break e;$l(i,l,d),s=u}else for(o=0;o<r.length;o++){if(l=r[o],u=l.instance,d=l.currentTarget,l=l.listener,u!==s&&i.isPropagationStopped())break e;$l(i,l,d),s=u}}}if(Zr)throw e=Rs,Zr=!1,Rs=null,e}function B(e,t){var n=t[Us];n===void 0&&(n=t[Us]=new Set);var r=e+"__bubble";n.has(r)||(Ma(t,e,2,!1),n.add(r))}function bi(e,t,n){var r=0;t&&(r|=4),Ma(n,e,r,t)}var Er="_reactListening"+Math.random().toString(36).slice(2);function bn(e){if(!e[Er]){e[Er]=!0,Vu.forEach(function(n){n!=="selectionchange"&&(Fd.has(n)||bi(n,!1,e),bi(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Er]||(t[Er]=!0,bi("selectionchange",!1,t))}}function Ma(e,t,n,r){switch(va(t)){case 1:var i=bf;break;case 4:i=ed;break;default:i=Po}n=i.bind(null,t,n,e),i=void 0,!Ls||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function es(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===i||u.nodeType===8&&u.parentNode===i))return;o=o.return}for(;l!==null;){if(o=Ot(l),o===null)return;if(u=o.tag,u===5||u===6){r=s=o;continue e}l=l.parentNode}}r=r.return}ia(function(){var d=s,g=Eo(n),y=[];e:{var m=Da.get(e);if(m!==void 0){var S=Ro,E=e;switch(e){case"keypress":if(Br(n)===0)break e;case"keydown":case"keyup":S=md;break;case"focusin":E="focus",S=Yi;break;case"focusout":E="blur",S=Yi;break;case"beforeblur":case"afterblur":S=Yi;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=Ll;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=rd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=vd;break;case La:case Ra:case ja:S=od;break;case Oa:S=xd;break;case"scroll":S=td;break;case"wheel":S=Sd;break;case"copy":case"cut":case"paste":S=ud;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=jl}var N=(t&4)!==0,M=!N&&e==="scroll",p=N?m!==null?m+"Capture":null:m;N=[];for(var a=d,f;a!==null;){f=a;var c=f.stateNode;if(f.tag===5&&c!==null&&(f=c,p!==null&&(c=Yn(a,p),c!=null&&N.push(er(a,c,f)))),M)break;a=a.return}0<N.length&&(m=new S(m,E,null,n,g),y.push({event:m,listeners:N}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",m&&n!==Ts&&(E=n.relatedTarget||n.fromElement)&&(Ot(E)||E[et]))break e;if((S||m)&&(m=g.window===g?g:(m=g.ownerDocument)?m.defaultView||m.parentWindow:window,S?(E=n.relatedTarget||n.toElement,S=d,E=E?Ot(E):null,E!==null&&(M=Ht(E),E!==M||E.tag!==5&&E.tag!==6)&&(E=null)):(S=null,E=d),S!==E)){if(N=Ll,c="onMouseLeave",p="onMouseEnter",a="mouse",(e==="pointerout"||e==="pointerover")&&(N=jl,c="onPointerLeave",p="onPointerEnter",a="pointer"),M=S==null?m:Jt(S),f=E==null?m:Jt(E),m=new N(c,a+"leave",S,n,g),m.target=M,m.relatedTarget=f,c=null,Ot(g)===d&&(N=new N(p,a+"enter",E,n,g),N.target=f,N.relatedTarget=M,c=N),M=c,S&&E)t:{for(N=S,p=E,a=0,f=N;f;f=Qt(f))a++;for(f=0,c=p;c;c=Qt(c))f++;for(;0<a-f;)N=Qt(N),a--;for(;0<f-a;)p=Qt(p),f--;for(;a--;){if(N===p||p!==null&&N===p.alternate)break t;N=Qt(N),p=Qt(p)}N=null}else N=null;S!==null&&Vl(y,m,S,N,!1),E!==null&&M!==null&&Vl(y,M,E,N,!0)}}e:{if(m=d?Jt(d):window,S=m.nodeName&&m.nodeName.toLowerCase(),S==="select"||S==="input"&&m.type==="file")var v=Ld;else if(zl(m))if(Ea)v=Dd;else{v=jd;var x=Rd}else(S=m.nodeName)&&S.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(v=Od);if(v&&(v=v(e,d))){_a(y,v,n,g);break e}x&&x(e,m,d),e==="focusout"&&(x=m._wrapperState)&&x.controlled&&m.type==="number"&&Ss(m,"number",m.value)}switch(x=d?Jt(d):window,e){case"focusin":(zl(x)||x.contentEditable==="true")&&(Xt=x,zs=d,Un=null);break;case"focusout":Un=zs=Xt=null;break;case"mousedown":Ms=!0;break;case"contextmenu":case"mouseup":case"dragend":Ms=!1,Fl(y,n,g);break;case"selectionchange":if(Ad)break;case"keydown":case"keyup":Fl(y,n,g)}var k;if(Oo)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else qt?ka(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(xa&&n.locale!=="ko"&&(qt||_!=="onCompositionStart"?_==="onCompositionEnd"&&qt&&(k=wa()):(ct=g,Lo="value"in ct?ct.value:ct.textContent,qt=!0)),x=ri(d,_),0<x.length&&(_=new Rl(_,e,null,n,g),y.push({event:_,listeners:x}),k?_.data=k:(k=Sa(n),k!==null&&(_.data=k)))),(k=Ed?Nd(e,n):Cd(e,n))&&(d=ri(d,"onBeforeInput"),0<d.length&&(g=new Rl("onBeforeInput","beforeinput",null,n,g),y.push({event:g,listeners:d}),g.data=k))}za(y,t)})}function er(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ri(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Yn(e,n),s!=null&&r.unshift(er(e,s,i)),s=Yn(e,t),s!=null&&r.push(er(e,s,i))),e=e.return}return r}function Qt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Vl(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var l=n,u=l.alternate,d=l.stateNode;if(u!==null&&u===r)break;l.tag===5&&d!==null&&(l=d,i?(u=Yn(n,s),u!=null&&o.unshift(er(n,u,l))):i||(u=Yn(n,s),u!=null&&o.push(er(n,u,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Ud=/\r\n?/g,$d=/\u0000|\uFFFD/g;function Hl(e){return(typeof e=="string"?e:""+e).replace(Ud,`
`).replace($d,"")}function Nr(e,t,n){if(t=Hl(t),Hl(e)!==t&&n)throw Error(w(425))}function ii(){}var As=null,Is=null;function Bs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Fs=typeof setTimeout=="function"?setTimeout:void 0,Vd=typeof clearTimeout=="function"?clearTimeout:void 0,Wl=typeof Promise=="function"?Promise:void 0,Hd=typeof queueMicrotask=="function"?queueMicrotask:typeof Wl<"u"?function(e){return Wl.resolve(null).then(e).catch(Wd)}:Fs;function Wd(e){setTimeout(function(){throw e})}function ts(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Gn(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Gn(t)}function mt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ql(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var xn=Math.random().toString(36).slice(2),He="__reactFiber$"+xn,tr="__reactProps$"+xn,et="__reactContainer$"+xn,Us="__reactEvents$"+xn,Qd="__reactListeners$"+xn,Kd="__reactHandles$"+xn;function Ot(e){var t=e[He];if(t)return t;for(var n=e.parentNode;n;){if(t=n[et]||n[He]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ql(e);e!==null;){if(n=e[He])return n;e=Ql(e)}return t}e=n,n=e.parentNode}return null}function fr(e){return e=e[He]||e[et],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Jt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(w(33))}function Ni(e){return e[tr]||null}var $s=[],Zt=-1;function Et(e){return{current:e}}function F(e){0>Zt||(e.current=$s[Zt],$s[Zt]=null,Zt--)}function I(e,t){Zt++,$s[Zt]=e.current,e.current=t}var St={},ue=Et(St),ye=Et(!1),It=St;function dn(e,t){var n=e.type.contextTypes;if(!n)return St;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function ge(e){return e=e.childContextTypes,e!=null}function si(){F(ye),F(ue)}function Kl(e,t,n){if(ue.current!==St)throw Error(w(168));I(ue,t),I(ye,n)}function Aa(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(w(108,Rf(e)||"Unknown",i));return W({},n,r)}function oi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||St,It=ue.current,I(ue,e),I(ye,ye.current),!0}function Yl(e,t,n){var r=e.stateNode;if(!r)throw Error(w(169));n?(e=Aa(e,t,It),r.__reactInternalMemoizedMergedChildContext=e,F(ye),F(ue),I(ue,e)):F(ye),I(ye,n)}var Xe=null,Ci=!1,ns=!1;function Ia(e){Xe===null?Xe=[e]:Xe.push(e)}function Yd(e){Ci=!0,Ia(e)}function Nt(){if(!ns&&Xe!==null){ns=!0;var e=0,t=A;try{var n=Xe;for(A=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Xe=null,Ci=!1}catch(i){throw Xe!==null&&(Xe=Xe.slice(e+1)),ua(No,Nt),i}finally{A=t,ns=!1}}return null}var bt=[],en=0,li=null,ui=0,Ne=[],Ce=0,Bt=null,Ge=1,Je="";function Rt(e,t){bt[en++]=ui,bt[en++]=li,li=e,ui=t}function Ba(e,t,n){Ne[Ce++]=Ge,Ne[Ce++]=Je,Ne[Ce++]=Bt,Bt=e;var r=Ge;e=Je;var i=32-Ie(r)-1;r&=~(1<<i),n+=1;var s=32-Ie(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Ge=1<<32-Ie(t)+i|n<<i|r,Je=s+e}else Ge=1<<s|n<<i|r,Je=e}function zo(e){e.return!==null&&(Rt(e,1),Ba(e,1,0))}function Mo(e){for(;e===li;)li=bt[--en],bt[en]=null,ui=bt[--en],bt[en]=null;for(;e===Bt;)Bt=Ne[--Ce],Ne[Ce]=null,Je=Ne[--Ce],Ne[Ce]=null,Ge=Ne[--Ce],Ne[Ce]=null}var ke=null,xe=null,U=!1,Ae=null;function Fa(e,t){var n=Pe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ql(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ke=e,xe=mt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ke=e,xe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Bt!==null?{id:Ge,overflow:Je}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Pe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ke=e,xe=null,!0):!1;default:return!1}}function Vs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Hs(e){if(U){var t=xe;if(t){var n=t;if(!ql(e,t)){if(Vs(e))throw Error(w(418));t=mt(n.nextSibling);var r=ke;t&&ql(e,t)?Fa(r,n):(e.flags=e.flags&-4097|2,U=!1,ke=e)}}else{if(Vs(e))throw Error(w(418));e.flags=e.flags&-4097|2,U=!1,ke=e}}}function Xl(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ke=e}function Cr(e){if(e!==ke)return!1;if(!U)return Xl(e),U=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Bs(e.type,e.memoizedProps)),t&&(t=xe)){if(Vs(e))throw Ua(),Error(w(418));for(;t;)Fa(e,t),t=mt(t.nextSibling)}if(Xl(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(w(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){xe=mt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}xe=null}}else xe=ke?mt(e.stateNode.nextSibling):null;return!0}function Ua(){for(var e=xe;e;)e=mt(e.nextSibling)}function pn(){xe=ke=null,U=!1}function Ao(e){Ae===null?Ae=[e]:Ae.push(e)}var qd=rt.ReactCurrentBatchConfig;function Tn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(w(309));var r=n.stateNode}if(!r)throw Error(w(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var l=i.refs;o===null?delete l[s]:l[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(w(284));if(!n._owner)throw Error(w(290,e))}return e}function Tr(e,t){throw e=Object.prototype.toString.call(t),Error(w(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Gl(e){var t=e._init;return t(e._payload)}function $a(e){function t(p,a){if(e){var f=p.deletions;f===null?(p.deletions=[a],p.flags|=16):f.push(a)}}function n(p,a){if(!e)return null;for(;a!==null;)t(p,a),a=a.sibling;return null}function r(p,a){for(p=new Map;a!==null;)a.key!==null?p.set(a.key,a):p.set(a.index,a),a=a.sibling;return p}function i(p,a){return p=wt(p,a),p.index=0,p.sibling=null,p}function s(p,a,f){return p.index=f,e?(f=p.alternate,f!==null?(f=f.index,f<a?(p.flags|=2,a):f):(p.flags|=2,a)):(p.flags|=1048576,a)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,a,f,c){return a===null||a.tag!==6?(a=as(f,p.mode,c),a.return=p,a):(a=i(a,f),a.return=p,a)}function u(p,a,f,c){var v=f.type;return v===Yt?g(p,a,f.props.children,c,f.key):a!==null&&(a.elementType===v||typeof v=="object"&&v!==null&&v.$$typeof===ot&&Gl(v)===a.type)?(c=i(a,f.props),c.ref=Tn(p,a,f),c.return=p,c):(c=Qr(f.type,f.key,f.props,null,p.mode,c),c.ref=Tn(p,a,f),c.return=p,c)}function d(p,a,f,c){return a===null||a.tag!==4||a.stateNode.containerInfo!==f.containerInfo||a.stateNode.implementation!==f.implementation?(a=cs(f,p.mode,c),a.return=p,a):(a=i(a,f.children||[]),a.return=p,a)}function g(p,a,f,c,v){return a===null||a.tag!==7?(a=At(f,p.mode,c,v),a.return=p,a):(a=i(a,f),a.return=p,a)}function y(p,a,f){if(typeof a=="string"&&a!==""||typeof a=="number")return a=as(""+a,p.mode,f),a.return=p,a;if(typeof a=="object"&&a!==null){switch(a.$$typeof){case yr:return f=Qr(a.type,a.key,a.props,null,p.mode,f),f.ref=Tn(p,null,a),f.return=p,f;case Kt:return a=cs(a,p.mode,f),a.return=p,a;case ot:var c=a._init;return y(p,c(a._payload),f)}if(On(a)||Sn(a))return a=At(a,p.mode,f,null),a.return=p,a;Tr(p,a)}return null}function m(p,a,f,c){var v=a!==null?a.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return v!==null?null:l(p,a,""+f,c);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case yr:return f.key===v?u(p,a,f,c):null;case Kt:return f.key===v?d(p,a,f,c):null;case ot:return v=f._init,m(p,a,v(f._payload),c)}if(On(f)||Sn(f))return v!==null?null:g(p,a,f,c,null);Tr(p,f)}return null}function S(p,a,f,c,v){if(typeof c=="string"&&c!==""||typeof c=="number")return p=p.get(f)||null,l(a,p,""+c,v);if(typeof c=="object"&&c!==null){switch(c.$$typeof){case yr:return p=p.get(c.key===null?f:c.key)||null,u(a,p,c,v);case Kt:return p=p.get(c.key===null?f:c.key)||null,d(a,p,c,v);case ot:var x=c._init;return S(p,a,f,x(c._payload),v)}if(On(c)||Sn(c))return p=p.get(f)||null,g(a,p,c,v,null);Tr(a,c)}return null}function E(p,a,f,c){for(var v=null,x=null,k=a,_=a=0,j=null;k!==null&&_<f.length;_++){k.index>_?(j=k,k=null):j=k.sibling;var P=m(p,k,f[_],c);if(P===null){k===null&&(k=j);break}e&&k&&P.alternate===null&&t(p,k),a=s(P,a,_),x===null?v=P:x.sibling=P,x=P,k=j}if(_===f.length)return n(p,k),U&&Rt(p,_),v;if(k===null){for(;_<f.length;_++)k=y(p,f[_],c),k!==null&&(a=s(k,a,_),x===null?v=k:x.sibling=k,x=k);return U&&Rt(p,_),v}for(k=r(p,k);_<f.length;_++)j=S(k,p,_,f[_],c),j!==null&&(e&&j.alternate!==null&&k.delete(j.key===null?_:j.key),a=s(j,a,_),x===null?v=j:x.sibling=j,x=j);return e&&k.forEach(function($){return t(p,$)}),U&&Rt(p,_),v}function N(p,a,f,c){var v=Sn(f);if(typeof v!="function")throw Error(w(150));if(f=v.call(f),f==null)throw Error(w(151));for(var x=v=null,k=a,_=a=0,j=null,P=f.next();k!==null&&!P.done;_++,P=f.next()){k.index>_?(j=k,k=null):j=k.sibling;var $=m(p,k,P.value,c);if($===null){k===null&&(k=j);break}e&&k&&$.alternate===null&&t(p,k),a=s($,a,_),x===null?v=$:x.sibling=$,x=$,k=j}if(P.done)return n(p,k),U&&Rt(p,_),v;if(k===null){for(;!P.done;_++,P=f.next())P=y(p,P.value,c),P!==null&&(a=s(P,a,_),x===null?v=P:x.sibling=P,x=P);return U&&Rt(p,_),v}for(k=r(p,k);!P.done;_++,P=f.next())P=S(k,p,_,P.value,c),P!==null&&(e&&P.alternate!==null&&k.delete(P.key===null?_:P.key),a=s(P,a,_),x===null?v=P:x.sibling=P,x=P);return e&&k.forEach(function(Ct){return t(p,Ct)}),U&&Rt(p,_),v}function M(p,a,f,c){if(typeof f=="object"&&f!==null&&f.type===Yt&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case yr:e:{for(var v=f.key,x=a;x!==null;){if(x.key===v){if(v=f.type,v===Yt){if(x.tag===7){n(p,x.sibling),a=i(x,f.props.children),a.return=p,p=a;break e}}else if(x.elementType===v||typeof v=="object"&&v!==null&&v.$$typeof===ot&&Gl(v)===x.type){n(p,x.sibling),a=i(x,f.props),a.ref=Tn(p,x,f),a.return=p,p=a;break e}n(p,x);break}else t(p,x);x=x.sibling}f.type===Yt?(a=At(f.props.children,p.mode,c,f.key),a.return=p,p=a):(c=Qr(f.type,f.key,f.props,null,p.mode,c),c.ref=Tn(p,a,f),c.return=p,p=c)}return o(p);case Kt:e:{for(x=f.key;a!==null;){if(a.key===x)if(a.tag===4&&a.stateNode.containerInfo===f.containerInfo&&a.stateNode.implementation===f.implementation){n(p,a.sibling),a=i(a,f.children||[]),a.return=p,p=a;break e}else{n(p,a);break}else t(p,a);a=a.sibling}a=cs(f,p.mode,c),a.return=p,p=a}return o(p);case ot:return x=f._init,M(p,a,x(f._payload),c)}if(On(f))return E(p,a,f,c);if(Sn(f))return N(p,a,f,c);Tr(p,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,a!==null&&a.tag===6?(n(p,a.sibling),a=i(a,f),a.return=p,p=a):(n(p,a),a=as(f,p.mode,c),a.return=p,p=a),o(p)):n(p,a)}return M}var hn=$a(!0),Va=$a(!1),ai=Et(null),ci=null,tn=null,Io=null;function Bo(){Io=tn=ci=null}function Fo(e){var t=ai.current;F(ai),e._currentValue=t}function Ws(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function an(e,t){ci=e,Io=tn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(me=!0),e.firstContext=null)}function Re(e){var t=e._currentValue;if(Io!==e)if(e={context:e,memoizedValue:t,next:null},tn===null){if(ci===null)throw Error(w(308));tn=e,ci.dependencies={lanes:0,firstContext:e}}else tn=tn.next=e;return t}var Dt=null;function Uo(e){Dt===null?Dt=[e]:Dt.push(e)}function Ha(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Uo(t)):(n.next=i.next,i.next=n),t.interleaved=n,tt(e,r)}function tt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var lt=!1;function $o(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Wa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ze(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function yt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,tt(e,n)}return i=r.interleaved,i===null?(t.next=t,Uo(r)):(t.next=i.next,i.next=t),r.interleaved=t,tt(e,n)}function Fr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Co(e,n)}}function Jl(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function fi(e,t,n,r){var i=e.updateQueue;lt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var u=l,d=u.next;u.next=null,o===null?s=d:o.next=d,o=u;var g=e.alternate;g!==null&&(g=g.updateQueue,l=g.lastBaseUpdate,l!==o&&(l===null?g.firstBaseUpdate=d:l.next=d,g.lastBaseUpdate=u))}if(s!==null){var y=i.baseState;o=0,g=d=u=null,l=s;do{var m=l.lane,S=l.eventTime;if((r&m)===m){g!==null&&(g=g.next={eventTime:S,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var E=e,N=l;switch(m=t,S=n,N.tag){case 1:if(E=N.payload,typeof E=="function"){y=E.call(S,y,m);break e}y=E;break e;case 3:E.flags=E.flags&-65537|128;case 0:if(E=N.payload,m=typeof E=="function"?E.call(S,y,m):E,m==null)break e;y=W({},y,m);break e;case 2:lt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,m=i.effects,m===null?i.effects=[l]:m.push(l))}else S={eventTime:S,lane:m,tag:l.tag,payload:l.payload,callback:l.callback,next:null},g===null?(d=g=S,u=y):g=g.next=S,o|=m;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;m=l,l=m.next,m.next=null,i.lastBaseUpdate=m,i.shared.pending=null}}while(!0);if(g===null&&(u=y),i.baseState=u,i.firstBaseUpdate=d,i.lastBaseUpdate=g,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);Ut|=o,e.lanes=o,e.memoizedState=y}}function Zl(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(w(191,i));i.call(r)}}}var dr={},Qe=Et(dr),nr=Et(dr),rr=Et(dr);function zt(e){if(e===dr)throw Error(w(174));return e}function Vo(e,t){switch(I(rr,t),I(nr,e),I(Qe,dr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Es(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Es(t,e)}F(Qe),I(Qe,t)}function mn(){F(Qe),F(nr),F(rr)}function Qa(e){zt(rr.current);var t=zt(Qe.current),n=Es(t,e.type);t!==n&&(I(nr,e),I(Qe,n))}function Ho(e){nr.current===e&&(F(Qe),F(nr))}var V=Et(0);function di(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var rs=[];function Wo(){for(var e=0;e<rs.length;e++)rs[e]._workInProgressVersionPrimary=null;rs.length=0}var Ur=rt.ReactCurrentDispatcher,is=rt.ReactCurrentBatchConfig,Ft=0,H=null,G=null,b=null,pi=!1,$n=!1,ir=0,Xd=0;function se(){throw Error(w(321))}function Qo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Fe(e[n],t[n]))return!1;return!0}function Ko(e,t,n,r,i,s){if(Ft=s,H=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ur.current=e===null||e.memoizedState===null?bd:ep,e=n(r,i),$n){s=0;do{if($n=!1,ir=0,25<=s)throw Error(w(301));s+=1,b=G=null,t.updateQueue=null,Ur.current=tp,e=n(r,i)}while($n)}if(Ur.current=hi,t=G!==null&&G.next!==null,Ft=0,b=G=H=null,pi=!1,t)throw Error(w(300));return e}function Yo(){var e=ir!==0;return ir=0,e}function Ve(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return b===null?H.memoizedState=b=e:b=b.next=e,b}function je(){if(G===null){var e=H.alternate;e=e!==null?e.memoizedState:null}else e=G.next;var t=b===null?H.memoizedState:b.next;if(t!==null)b=t,G=e;else{if(e===null)throw Error(w(310));G=e,e={memoizedState:G.memoizedState,baseState:G.baseState,baseQueue:G.baseQueue,queue:G.queue,next:null},b===null?H.memoizedState=b=e:b=b.next=e}return b}function sr(e,t){return typeof t=="function"?t(e):t}function ss(e){var t=je(),n=t.queue;if(n===null)throw Error(w(311));n.lastRenderedReducer=e;var r=G,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var l=o=null,u=null,d=s;do{var g=d.lane;if((Ft&g)===g)u!==null&&(u=u.next={lane:0,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null}),r=d.hasEagerState?d.eagerState:e(r,d.action);else{var y={lane:g,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null};u===null?(l=u=y,o=r):u=u.next=y,H.lanes|=g,Ut|=g}d=d.next}while(d!==null&&d!==s);u===null?o=r:u.next=l,Fe(r,t.memoizedState)||(me=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,H.lanes|=s,Ut|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function os(e){var t=je(),n=t.queue;if(n===null)throw Error(w(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);Fe(s,t.memoizedState)||(me=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Ka(){}function Ya(e,t){var n=H,r=je(),i=t(),s=!Fe(r.memoizedState,i);if(s&&(r.memoizedState=i,me=!0),r=r.queue,qo(Ga.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||b!==null&&b.memoizedState.tag&1){if(n.flags|=2048,or(9,Xa.bind(null,n,r,i,t),void 0,null),ee===null)throw Error(w(349));Ft&30||qa(n,t,i)}return i}function qa(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=H.updateQueue,t===null?(t={lastEffect:null,stores:null},H.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Xa(e,t,n,r){t.value=n,t.getSnapshot=r,Ja(t)&&Za(e)}function Ga(e,t,n){return n(function(){Ja(t)&&Za(e)})}function Ja(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Fe(e,n)}catch{return!0}}function Za(e){var t=tt(e,1);t!==null&&Be(t,e,1,-1)}function bl(e){var t=Ve();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:sr,lastRenderedState:e},t.queue=e,e=e.dispatch=Zd.bind(null,H,e),[t.memoizedState,e]}function or(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=H.updateQueue,t===null?(t={lastEffect:null,stores:null},H.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ba(){return je().memoizedState}function $r(e,t,n,r){var i=Ve();H.flags|=e,i.memoizedState=or(1|t,n,void 0,r===void 0?null:r)}function Ti(e,t,n,r){var i=je();r=r===void 0?null:r;var s=void 0;if(G!==null){var o=G.memoizedState;if(s=o.destroy,r!==null&&Qo(r,o.deps)){i.memoizedState=or(t,n,s,r);return}}H.flags|=e,i.memoizedState=or(1|t,n,s,r)}function eu(e,t){return $r(8390656,8,e,t)}function qo(e,t){return Ti(2048,8,e,t)}function ec(e,t){return Ti(4,2,e,t)}function tc(e,t){return Ti(4,4,e,t)}function nc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function rc(e,t,n){return n=n!=null?n.concat([e]):null,Ti(4,4,nc.bind(null,t,e),n)}function Xo(){}function ic(e,t){var n=je();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Qo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function sc(e,t){var n=je();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Qo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function oc(e,t,n){return Ft&21?(Fe(n,t)||(n=fa(),H.lanes|=n,Ut|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,me=!0),e.memoizedState=n)}function Gd(e,t){var n=A;A=n!==0&&4>n?n:4,e(!0);var r=is.transition;is.transition={};try{e(!1),t()}finally{A=n,is.transition=r}}function lc(){return je().memoizedState}function Jd(e,t,n){var r=vt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},uc(e))ac(t,n);else if(n=Ha(e,t,n,r),n!==null){var i=ce();Be(n,e,r,i),cc(n,t,r)}}function Zd(e,t,n){var r=vt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(uc(e))ac(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,l=s(o,n);if(i.hasEagerState=!0,i.eagerState=l,Fe(l,o)){var u=t.interleaved;u===null?(i.next=i,Uo(t)):(i.next=u.next,u.next=i),t.interleaved=i;return}}catch{}finally{}n=Ha(e,t,i,r),n!==null&&(i=ce(),Be(n,e,r,i),cc(n,t,r))}}function uc(e){var t=e.alternate;return e===H||t!==null&&t===H}function ac(e,t){$n=pi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function cc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Co(e,n)}}var hi={readContext:Re,useCallback:se,useContext:se,useEffect:se,useImperativeHandle:se,useInsertionEffect:se,useLayoutEffect:se,useMemo:se,useReducer:se,useRef:se,useState:se,useDebugValue:se,useDeferredValue:se,useTransition:se,useMutableSource:se,useSyncExternalStore:se,useId:se,unstable_isNewReconciler:!1},bd={readContext:Re,useCallback:function(e,t){return Ve().memoizedState=[e,t===void 0?null:t],e},useContext:Re,useEffect:eu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,$r(4194308,4,nc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return $r(4194308,4,e,t)},useInsertionEffect:function(e,t){return $r(4,2,e,t)},useMemo:function(e,t){var n=Ve();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ve();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Jd.bind(null,H,e),[r.memoizedState,e]},useRef:function(e){var t=Ve();return e={current:e},t.memoizedState=e},useState:bl,useDebugValue:Xo,useDeferredValue:function(e){return Ve().memoizedState=e},useTransition:function(){var e=bl(!1),t=e[0];return e=Gd.bind(null,e[1]),Ve().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=H,i=Ve();if(U){if(n===void 0)throw Error(w(407));n=n()}else{if(n=t(),ee===null)throw Error(w(349));Ft&30||qa(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,eu(Ga.bind(null,r,s,e),[e]),r.flags|=2048,or(9,Xa.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=Ve(),t=ee.identifierPrefix;if(U){var n=Je,r=Ge;n=(r&~(1<<32-Ie(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ir++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Xd++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ep={readContext:Re,useCallback:ic,useContext:Re,useEffect:qo,useImperativeHandle:rc,useInsertionEffect:ec,useLayoutEffect:tc,useMemo:sc,useReducer:ss,useRef:ba,useState:function(){return ss(sr)},useDebugValue:Xo,useDeferredValue:function(e){var t=je();return oc(t,G.memoizedState,e)},useTransition:function(){var e=ss(sr)[0],t=je().memoizedState;return[e,t]},useMutableSource:Ka,useSyncExternalStore:Ya,useId:lc,unstable_isNewReconciler:!1},tp={readContext:Re,useCallback:ic,useContext:Re,useEffect:qo,useImperativeHandle:rc,useInsertionEffect:ec,useLayoutEffect:tc,useMemo:sc,useReducer:os,useRef:ba,useState:function(){return os(sr)},useDebugValue:Xo,useDeferredValue:function(e){var t=je();return G===null?t.memoizedState=e:oc(t,G.memoizedState,e)},useTransition:function(){var e=os(sr)[0],t=je().memoizedState;return[e,t]},useMutableSource:Ka,useSyncExternalStore:Ya,useId:lc,unstable_isNewReconciler:!1};function De(e,t){if(e&&e.defaultProps){t=W({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Qs(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:W({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Pi={isMounted:function(e){return(e=e._reactInternals)?Ht(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ce(),i=vt(e),s=Ze(r,i);s.payload=t,n!=null&&(s.callback=n),t=yt(e,s,i),t!==null&&(Be(t,e,i,r),Fr(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ce(),i=vt(e),s=Ze(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=yt(e,s,i),t!==null&&(Be(t,e,i,r),Fr(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ce(),r=vt(e),i=Ze(n,r);i.tag=2,t!=null&&(i.callback=t),t=yt(e,i,r),t!==null&&(Be(t,e,r,n),Fr(t,e,r))}};function tu(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Zn(n,r)||!Zn(i,s):!0}function fc(e,t,n){var r=!1,i=St,s=t.contextType;return typeof s=="object"&&s!==null?s=Re(s):(i=ge(t)?It:ue.current,r=t.contextTypes,s=(r=r!=null)?dn(e,i):St),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Pi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function nu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Pi.enqueueReplaceState(t,t.state,null)}function Ks(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},$o(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Re(s):(s=ge(t)?It:ue.current,i.context=dn(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Qs(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Pi.enqueueReplaceState(i,i.state,null),fi(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function yn(e,t){try{var n="",r=t;do n+=Lf(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function ls(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ys(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var np=typeof WeakMap=="function"?WeakMap:Map;function dc(e,t,n){n=Ze(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){yi||(yi=!0,ro=r),Ys(e,t)},n}function pc(e,t,n){n=Ze(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Ys(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Ys(e,t),typeof r!="function"&&(gt===null?gt=new Set([this]):gt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function ru(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new np;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=yp.bind(null,e,t,n),t.then(e,e))}function iu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function su(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ze(-1,1),t.tag=2,yt(n,t,1))),n.lanes|=1),e)}var rp=rt.ReactCurrentOwner,me=!1;function ae(e,t,n,r){t.child=e===null?Va(t,null,n,r):hn(t,e.child,n,r)}function ou(e,t,n,r,i){n=n.render;var s=t.ref;return an(t,i),r=Ko(e,t,n,r,s,i),n=Yo(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,nt(e,t,i)):(U&&n&&zo(t),t.flags|=1,ae(e,t,r,i),t.child)}function lu(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!rl(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,hc(e,t,s,r,i)):(e=Qr(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Zn,n(o,r)&&e.ref===t.ref)return nt(e,t,i)}return t.flags|=1,e=wt(s,r),e.ref=t.ref,e.return=t,t.child=e}function hc(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Zn(s,r)&&e.ref===t.ref)if(me=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(me=!0);else return t.lanes=e.lanes,nt(e,t,i)}return qs(e,t,n,r,i)}function mc(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},I(rn,we),we|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,I(rn,we),we|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,I(rn,we),we|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,I(rn,we),we|=r;return ae(e,t,i,n),t.child}function yc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function qs(e,t,n,r,i){var s=ge(n)?It:ue.current;return s=dn(t,s),an(t,i),n=Ko(e,t,n,r,s,i),r=Yo(),e!==null&&!me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,nt(e,t,i)):(U&&r&&zo(t),t.flags|=1,ae(e,t,n,i),t.child)}function uu(e,t,n,r,i){if(ge(n)){var s=!0;oi(t)}else s=!1;if(an(t,i),t.stateNode===null)Vr(e,t),fc(t,n,r),Ks(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var u=o.context,d=n.contextType;typeof d=="object"&&d!==null?d=Re(d):(d=ge(n)?It:ue.current,d=dn(t,d));var g=n.getDerivedStateFromProps,y=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function";y||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||u!==d)&&nu(t,o,r,d),lt=!1;var m=t.memoizedState;o.state=m,fi(t,r,o,i),u=t.memoizedState,l!==r||m!==u||ye.current||lt?(typeof g=="function"&&(Qs(t,n,g,r),u=t.memoizedState),(l=lt||tu(t,n,l,r,m,u,d))?(y||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=d,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Wa(e,t),l=t.memoizedProps,d=t.type===t.elementType?l:De(t.type,l),o.props=d,y=t.pendingProps,m=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=Re(u):(u=ge(n)?It:ue.current,u=dn(t,u));var S=n.getDerivedStateFromProps;(g=typeof S=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==y||m!==u)&&nu(t,o,r,u),lt=!1,m=t.memoizedState,o.state=m,fi(t,r,o,i);var E=t.memoizedState;l!==y||m!==E||ye.current||lt?(typeof S=="function"&&(Qs(t,n,S,r),E=t.memoizedState),(d=lt||tu(t,n,d,r,m,E,u)||!1)?(g||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,E,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,E,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=E),o.props=r,o.state=E,o.context=u,r=d):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return Xs(e,t,n,r,s,i)}function Xs(e,t,n,r,i,s){yc(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&Yl(t,n,!1),nt(e,t,s);r=t.stateNode,rp.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=hn(t,e.child,null,s),t.child=hn(t,null,l,s)):ae(e,t,l,s),t.memoizedState=r.state,i&&Yl(t,n,!0),t.child}function gc(e){var t=e.stateNode;t.pendingContext?Kl(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Kl(e,t.context,!1),Vo(e,t.containerInfo)}function au(e,t,n,r,i){return pn(),Ao(i),t.flags|=256,ae(e,t,n,r),t.child}var Gs={dehydrated:null,treeContext:null,retryLane:0};function Js(e){return{baseLanes:e,cachePool:null,transitions:null}}function vc(e,t,n){var r=t.pendingProps,i=V.current,s=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),I(V,i&1),e===null)return Hs(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=ji(o,r,0,null),e=At(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Js(n),t.memoizedState=Gs,e):Go(t,o));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return ip(e,t,o,r,l,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,l=i.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=wt(i,u),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?s=wt(l,s):(s=At(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?Js(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=Gs,r}return s=e.child,e=s.sibling,r=wt(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Go(e,t){return t=ji({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Pr(e,t,n,r){return r!==null&&Ao(r),hn(t,e.child,null,n),e=Go(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ip(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=ls(Error(w(422))),Pr(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=ji({mode:"visible",children:r.children},i,0,null),s=At(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&hn(t,e.child,null,o),t.child.memoizedState=Js(o),t.memoizedState=Gs,s);if(!(t.mode&1))return Pr(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(w(419)),r=ls(s,r,void 0),Pr(e,t,o,r)}if(l=(o&e.childLanes)!==0,me||l){if(r=ee,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,tt(e,i),Be(r,e,i,-1))}return nl(),r=ls(Error(w(421))),Pr(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=gp.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,xe=mt(i.nextSibling),ke=t,U=!0,Ae=null,e!==null&&(Ne[Ce++]=Ge,Ne[Ce++]=Je,Ne[Ce++]=Bt,Ge=e.id,Je=e.overflow,Bt=t),t=Go(t,r.children),t.flags|=4096,t)}function cu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ws(e.return,t,n)}function us(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function wc(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(ae(e,t,r.children,n),r=V.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&cu(e,n,t);else if(e.tag===19)cu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(I(V,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&di(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),us(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&di(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}us(t,!0,n,null,s);break;case"together":us(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function nt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ut|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(w(153));if(t.child!==null){for(e=t.child,n=wt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=wt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function sp(e,t,n){switch(t.tag){case 3:gc(t),pn();break;case 5:Qa(t);break;case 1:ge(t.type)&&oi(t);break;case 4:Vo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;I(ai,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(I(V,V.current&1),t.flags|=128,null):n&t.child.childLanes?vc(e,t,n):(I(V,V.current&1),e=nt(e,t,n),e!==null?e.sibling:null);I(V,V.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return wc(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),I(V,V.current),r)break;return null;case 22:case 23:return t.lanes=0,mc(e,t,n)}return nt(e,t,n)}var xc,Zs,kc,Sc;xc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Zs=function(){};kc=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,zt(Qe.current);var s=null;switch(n){case"input":i=xs(e,i),r=xs(e,r),s=[];break;case"select":i=W({},i,{value:void 0}),r=W({},r,{value:void 0}),s=[];break;case"textarea":i=_s(e,i),r=_s(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ii)}Ns(n,r);var o;n=null;for(d in i)if(!r.hasOwnProperty(d)&&i.hasOwnProperty(d)&&i[d]!=null)if(d==="style"){var l=i[d];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else d!=="dangerouslySetInnerHTML"&&d!=="children"&&d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(Qn.hasOwnProperty(d)?s||(s=[]):(s=s||[]).push(d,null));for(d in r){var u=r[d];if(l=i!=null?i[d]:void 0,r.hasOwnProperty(d)&&u!==l&&(u!=null||l!=null))if(d==="style")if(l){for(o in l)!l.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&l[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(s||(s=[]),s.push(d,n)),n=u;else d==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,l=l?l.__html:void 0,u!=null&&l!==u&&(s=s||[]).push(d,u)):d==="children"?typeof u!="string"&&typeof u!="number"||(s=s||[]).push(d,""+u):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&(Qn.hasOwnProperty(d)?(u!=null&&d==="onScroll"&&B("scroll",e),s||l===u||(s=[])):(s=s||[]).push(d,u))}n&&(s=s||[]).push("style",n);var d=s;(t.updateQueue=d)&&(t.flags|=4)}};Sc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Pn(e,t){if(!U)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function oe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function op(e,t,n){var r=t.pendingProps;switch(Mo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return oe(t),null;case 1:return ge(t.type)&&si(),oe(t),null;case 3:return r=t.stateNode,mn(),F(ye),F(ue),Wo(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Cr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ae!==null&&(oo(Ae),Ae=null))),Zs(e,t),oe(t),null;case 5:Ho(t);var i=zt(rr.current);if(n=t.type,e!==null&&t.stateNode!=null)kc(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(w(166));return oe(t),null}if(e=zt(Qe.current),Cr(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[He]=t,r[tr]=s,e=(t.mode&1)!==0,n){case"dialog":B("cancel",r),B("close",r);break;case"iframe":case"object":case"embed":B("load",r);break;case"video":case"audio":for(i=0;i<zn.length;i++)B(zn[i],r);break;case"source":B("error",r);break;case"img":case"image":case"link":B("error",r),B("load",r);break;case"details":B("toggle",r);break;case"input":wl(r,s),B("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},B("invalid",r);break;case"textarea":kl(r,s),B("invalid",r)}Ns(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&Nr(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&Nr(r.textContent,l,e),i=["children",""+l]):Qn.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&B("scroll",r)}switch(n){case"input":gr(r),xl(r,s,!0);break;case"textarea":gr(r),Sl(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ii)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Gu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[He]=t,e[tr]=r,xc(e,t,!1,!1),t.stateNode=e;e:{switch(o=Cs(n,r),n){case"dialog":B("cancel",e),B("close",e),i=r;break;case"iframe":case"object":case"embed":B("load",e),i=r;break;case"video":case"audio":for(i=0;i<zn.length;i++)B(zn[i],e);i=r;break;case"source":B("error",e),i=r;break;case"img":case"image":case"link":B("error",e),B("load",e),i=r;break;case"details":B("toggle",e),i=r;break;case"input":wl(e,r),i=xs(e,r),B("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=W({},r,{value:void 0}),B("invalid",e);break;case"textarea":kl(e,r),i=_s(e,r),B("invalid",e);break;default:i=r}Ns(n,i),l=i;for(s in l)if(l.hasOwnProperty(s)){var u=l[s];s==="style"?bu(e,u):s==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Ju(e,u)):s==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Kn(e,u):typeof u=="number"&&Kn(e,""+u):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Qn.hasOwnProperty(s)?u!=null&&s==="onScroll"&&B("scroll",e):u!=null&&xo(e,s,u,o))}switch(n){case"input":gr(e),xl(e,r,!1);break;case"textarea":gr(e),Sl(e);break;case"option":r.value!=null&&e.setAttribute("value",""+kt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?sn(e,!!r.multiple,s,!1):r.defaultValue!=null&&sn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ii)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return oe(t),null;case 6:if(e&&t.stateNode!=null)Sc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(w(166));if(n=zt(rr.current),zt(Qe.current),Cr(t)){if(r=t.stateNode,n=t.memoizedProps,r[He]=t,(s=r.nodeValue!==n)&&(e=ke,e!==null))switch(e.tag){case 3:Nr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Nr(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[He]=t,t.stateNode=r}return oe(t),null;case 13:if(F(V),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(U&&xe!==null&&t.mode&1&&!(t.flags&128))Ua(),pn(),t.flags|=98560,s=!1;else if(s=Cr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(w(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(w(317));s[He]=t}else pn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;oe(t),s=!1}else Ae!==null&&(oo(Ae),Ae=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||V.current&1?J===0&&(J=3):nl())),t.updateQueue!==null&&(t.flags|=4),oe(t),null);case 4:return mn(),Zs(e,t),e===null&&bn(t.stateNode.containerInfo),oe(t),null;case 10:return Fo(t.type._context),oe(t),null;case 17:return ge(t.type)&&si(),oe(t),null;case 19:if(F(V),s=t.memoizedState,s===null)return oe(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)Pn(s,!1);else{if(J!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=di(e),o!==null){for(t.flags|=128,Pn(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return I(V,V.current&1|2),t.child}e=e.sibling}s.tail!==null&&Y()>gn&&(t.flags|=128,r=!0,Pn(s,!1),t.lanes=4194304)}else{if(!r)if(e=di(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Pn(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!U)return oe(t),null}else 2*Y()-s.renderingStartTime>gn&&n!==1073741824&&(t.flags|=128,r=!0,Pn(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Y(),t.sibling=null,n=V.current,I(V,r?n&1|2:n&1),t):(oe(t),null);case 22:case 23:return tl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?we&1073741824&&(oe(t),t.subtreeFlags&6&&(t.flags|=8192)):oe(t),null;case 24:return null;case 25:return null}throw Error(w(156,t.tag))}function lp(e,t){switch(Mo(t),t.tag){case 1:return ge(t.type)&&si(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mn(),F(ye),F(ue),Wo(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ho(t),null;case 13:if(F(V),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(w(340));pn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(V),null;case 4:return mn(),null;case 10:return Fo(t.type._context),null;case 22:case 23:return tl(),null;case 24:return null;default:return null}}var Lr=!1,le=!1,up=typeof WeakSet=="function"?WeakSet:Set,C=null;function nn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Q(e,t,r)}else n.current=null}function bs(e,t,n){try{n()}catch(r){Q(e,t,r)}}var fu=!1;function ap(e,t){if(As=ti,e=Ta(),Do(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,l=-1,u=-1,d=0,g=0,y=e,m=null;t:for(;;){for(var S;y!==n||i!==0&&y.nodeType!==3||(l=o+i),y!==s||r!==0&&y.nodeType!==3||(u=o+r),y.nodeType===3&&(o+=y.nodeValue.length),(S=y.firstChild)!==null;)m=y,y=S;for(;;){if(y===e)break t;if(m===n&&++d===i&&(l=o),m===s&&++g===r&&(u=o),(S=y.nextSibling)!==null)break;y=m,m=y.parentNode}y=S}n=l===-1||u===-1?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Is={focusedElem:e,selectionRange:n},ti=!1,C=t;C!==null;)if(t=C,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,C=e;else for(;C!==null;){t=C;try{var E=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(E!==null){var N=E.memoizedProps,M=E.memoizedState,p=t.stateNode,a=p.getSnapshotBeforeUpdate(t.elementType===t.type?N:De(t.type,N),M);p.__reactInternalSnapshotBeforeUpdate=a}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(w(163))}}catch(c){Q(t,t.return,c)}if(e=t.sibling,e!==null){e.return=t.return,C=e;break}C=t.return}return E=fu,fu=!1,E}function Vn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&bs(t,n,s)}i=i.next}while(i!==r)}}function Li(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function eo(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function _c(e){var t=e.alternate;t!==null&&(e.alternate=null,_c(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[He],delete t[tr],delete t[Us],delete t[Qd],delete t[Kd])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ec(e){return e.tag===5||e.tag===3||e.tag===4}function du(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ec(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function to(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ii));else if(r!==4&&(e=e.child,e!==null))for(to(e,t,n),e=e.sibling;e!==null;)to(e,t,n),e=e.sibling}function no(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(no(e,t,n),e=e.sibling;e!==null;)no(e,t,n),e=e.sibling}var te=null,ze=!1;function st(e,t,n){for(n=n.child;n!==null;)Nc(e,t,n),n=n.sibling}function Nc(e,t,n){if(We&&typeof We.onCommitFiberUnmount=="function")try{We.onCommitFiberUnmount(ki,n)}catch{}switch(n.tag){case 5:le||nn(n,t);case 6:var r=te,i=ze;te=null,st(e,t,n),te=r,ze=i,te!==null&&(ze?(e=te,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):te.removeChild(n.stateNode));break;case 18:te!==null&&(ze?(e=te,n=n.stateNode,e.nodeType===8?ts(e.parentNode,n):e.nodeType===1&&ts(e,n),Gn(e)):ts(te,n.stateNode));break;case 4:r=te,i=ze,te=n.stateNode.containerInfo,ze=!0,st(e,t,n),te=r,ze=i;break;case 0:case 11:case 14:case 15:if(!le&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&bs(n,t,o),i=i.next}while(i!==r)}st(e,t,n);break;case 1:if(!le&&(nn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Q(n,t,l)}st(e,t,n);break;case 21:st(e,t,n);break;case 22:n.mode&1?(le=(r=le)||n.memoizedState!==null,st(e,t,n),le=r):st(e,t,n);break;default:st(e,t,n)}}function pu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new up),t.forEach(function(r){var i=vp.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Oe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:te=l.stateNode,ze=!1;break e;case 3:te=l.stateNode.containerInfo,ze=!0;break e;case 4:te=l.stateNode.containerInfo,ze=!0;break e}l=l.return}if(te===null)throw Error(w(160));Nc(s,o,i),te=null,ze=!1;var u=i.alternate;u!==null&&(u.return=null),i.return=null}catch(d){Q(i,t,d)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Cc(t,e),t=t.sibling}function Cc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Oe(t,e),$e(e),r&4){try{Vn(3,e,e.return),Li(3,e)}catch(N){Q(e,e.return,N)}try{Vn(5,e,e.return)}catch(N){Q(e,e.return,N)}}break;case 1:Oe(t,e),$e(e),r&512&&n!==null&&nn(n,n.return);break;case 5:if(Oe(t,e),$e(e),r&512&&n!==null&&nn(n,n.return),e.flags&32){var i=e.stateNode;try{Kn(i,"")}catch(N){Q(e,e.return,N)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,l=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&qu(i,s),Cs(l,o);var d=Cs(l,s);for(o=0;o<u.length;o+=2){var g=u[o],y=u[o+1];g==="style"?bu(i,y):g==="dangerouslySetInnerHTML"?Ju(i,y):g==="children"?Kn(i,y):xo(i,g,y,d)}switch(l){case"input":ks(i,s);break;case"textarea":Xu(i,s);break;case"select":var m=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var S=s.value;S!=null?sn(i,!!s.multiple,S,!1):m!==!!s.multiple&&(s.defaultValue!=null?sn(i,!!s.multiple,s.defaultValue,!0):sn(i,!!s.multiple,s.multiple?[]:"",!1))}i[tr]=s}catch(N){Q(e,e.return,N)}}break;case 6:if(Oe(t,e),$e(e),r&4){if(e.stateNode===null)throw Error(w(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(N){Q(e,e.return,N)}}break;case 3:if(Oe(t,e),$e(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Gn(t.containerInfo)}catch(N){Q(e,e.return,N)}break;case 4:Oe(t,e),$e(e);break;case 13:Oe(t,e),$e(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(bo=Y())),r&4&&pu(e);break;case 22:if(g=n!==null&&n.memoizedState!==null,e.mode&1?(le=(d=le)||g,Oe(t,e),le=d):Oe(t,e),$e(e),r&8192){if(d=e.memoizedState!==null,(e.stateNode.isHidden=d)&&!g&&e.mode&1)for(C=e,g=e.child;g!==null;){for(y=C=g;C!==null;){switch(m=C,S=m.child,m.tag){case 0:case 11:case 14:case 15:Vn(4,m,m.return);break;case 1:nn(m,m.return);var E=m.stateNode;if(typeof E.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,E.props=t.memoizedProps,E.state=t.memoizedState,E.componentWillUnmount()}catch(N){Q(r,n,N)}}break;case 5:nn(m,m.return);break;case 22:if(m.memoizedState!==null){mu(y);continue}}S!==null?(S.return=m,C=S):mu(y)}g=g.sibling}e:for(g=null,y=e;;){if(y.tag===5){if(g===null){g=y;try{i=y.stateNode,d?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=y.stateNode,u=y.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,l.style.display=Zu("display",o))}catch(N){Q(e,e.return,N)}}}else if(y.tag===6){if(g===null)try{y.stateNode.nodeValue=d?"":y.memoizedProps}catch(N){Q(e,e.return,N)}}else if((y.tag!==22&&y.tag!==23||y.memoizedState===null||y===e)&&y.child!==null){y.child.return=y,y=y.child;continue}if(y===e)break e;for(;y.sibling===null;){if(y.return===null||y.return===e)break e;g===y&&(g=null),y=y.return}g===y&&(g=null),y.sibling.return=y.return,y=y.sibling}}break;case 19:Oe(t,e),$e(e),r&4&&pu(e);break;case 21:break;default:Oe(t,e),$e(e)}}function $e(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ec(n)){var r=n;break e}n=n.return}throw Error(w(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Kn(i,""),r.flags&=-33);var s=du(e);no(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,l=du(e);to(e,l,o);break;default:throw Error(w(161))}}catch(u){Q(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function cp(e,t,n){C=e,Tc(e)}function Tc(e,t,n){for(var r=(e.mode&1)!==0;C!==null;){var i=C,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Lr;if(!o){var l=i.alternate,u=l!==null&&l.memoizedState!==null||le;l=Lr;var d=le;if(Lr=o,(le=u)&&!d)for(C=i;C!==null;)o=C,u=o.child,o.tag===22&&o.memoizedState!==null?yu(i):u!==null?(u.return=o,C=u):yu(i);for(;s!==null;)C=s,Tc(s),s=s.sibling;C=i,Lr=l,le=d}hu(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,C=s):hu(e)}}function hu(e){for(;C!==null;){var t=C;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:le||Li(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!le)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:De(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Zl(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Zl(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var d=t.alternate;if(d!==null){var g=d.memoizedState;if(g!==null){var y=g.dehydrated;y!==null&&Gn(y)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(w(163))}le||t.flags&512&&eo(t)}catch(m){Q(t,t.return,m)}}if(t===e){C=null;break}if(n=t.sibling,n!==null){n.return=t.return,C=n;break}C=t.return}}function mu(e){for(;C!==null;){var t=C;if(t===e){C=null;break}var n=t.sibling;if(n!==null){n.return=t.return,C=n;break}C=t.return}}function yu(e){for(;C!==null;){var t=C;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Li(4,t)}catch(u){Q(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(u){Q(t,i,u)}}var s=t.return;try{eo(t)}catch(u){Q(t,s,u)}break;case 5:var o=t.return;try{eo(t)}catch(u){Q(t,o,u)}}}catch(u){Q(t,t.return,u)}if(t===e){C=null;break}var l=t.sibling;if(l!==null){l.return=t.return,C=l;break}C=t.return}}var fp=Math.ceil,mi=rt.ReactCurrentDispatcher,Jo=rt.ReactCurrentOwner,Le=rt.ReactCurrentBatchConfig,z=0,ee=null,q=null,re=0,we=0,rn=Et(0),J=0,lr=null,Ut=0,Ri=0,Zo=0,Hn=null,he=null,bo=0,gn=1/0,qe=null,yi=!1,ro=null,gt=null,Rr=!1,ft=null,gi=0,Wn=0,io=null,Hr=-1,Wr=0;function ce(){return z&6?Y():Hr!==-1?Hr:Hr=Y()}function vt(e){return e.mode&1?z&2&&re!==0?re&-re:qd.transition!==null?(Wr===0&&(Wr=fa()),Wr):(e=A,e!==0||(e=window.event,e=e===void 0?16:va(e.type)),e):1}function Be(e,t,n,r){if(50<Wn)throw Wn=0,io=null,Error(w(185));ar(e,n,r),(!(z&2)||e!==ee)&&(e===ee&&(!(z&2)&&(Ri|=n),J===4&&at(e,re)),ve(e,r),n===1&&z===0&&!(t.mode&1)&&(gn=Y()+500,Ci&&Nt()))}function ve(e,t){var n=e.callbackNode;qf(e,t);var r=ei(e,e===ee?re:0);if(r===0)n!==null&&Nl(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Nl(n),t===1)e.tag===0?Yd(gu.bind(null,e)):Ia(gu.bind(null,e)),Hd(function(){!(z&6)&&Nt()}),n=null;else{switch(da(r)){case 1:n=No;break;case 4:n=aa;break;case 16:n=br;break;case 536870912:n=ca;break;default:n=br}n=Mc(n,Pc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Pc(e,t){if(Hr=-1,Wr=0,z&6)throw Error(w(327));var n=e.callbackNode;if(cn()&&e.callbackNode!==n)return null;var r=ei(e,e===ee?re:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=vi(e,r);else{t=r;var i=z;z|=2;var s=Rc();(ee!==e||re!==t)&&(qe=null,gn=Y()+500,Mt(e,t));do try{hp();break}catch(l){Lc(e,l)}while(!0);Bo(),mi.current=s,z=i,q!==null?t=0:(ee=null,re=0,t=J)}if(t!==0){if(t===2&&(i=js(e),i!==0&&(r=i,t=so(e,i))),t===1)throw n=lr,Mt(e,0),at(e,r),ve(e,Y()),n;if(t===6)at(e,r);else{if(i=e.current.alternate,!(r&30)&&!dp(i)&&(t=vi(e,r),t===2&&(s=js(e),s!==0&&(r=s,t=so(e,s))),t===1))throw n=lr,Mt(e,0),at(e,r),ve(e,Y()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(w(345));case 2:jt(e,he,qe);break;case 3:if(at(e,r),(r&130023424)===r&&(t=bo+500-Y(),10<t)){if(ei(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ce(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Fs(jt.bind(null,e,he,qe),t);break}jt(e,he,qe);break;case 4:if(at(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-Ie(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*fp(r/1960))-r,10<r){e.timeoutHandle=Fs(jt.bind(null,e,he,qe),r);break}jt(e,he,qe);break;case 5:jt(e,he,qe);break;default:throw Error(w(329))}}}return ve(e,Y()),e.callbackNode===n?Pc.bind(null,e):null}function so(e,t){var n=Hn;return e.current.memoizedState.isDehydrated&&(Mt(e,t).flags|=256),e=vi(e,t),e!==2&&(t=he,he=n,t!==null&&oo(t)),e}function oo(e){he===null?he=e:he.push.apply(he,e)}function dp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!Fe(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function at(e,t){for(t&=~Zo,t&=~Ri,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ie(t),r=1<<n;e[n]=-1,t&=~r}}function gu(e){if(z&6)throw Error(w(327));cn();var t=ei(e,0);if(!(t&1))return ve(e,Y()),null;var n=vi(e,t);if(e.tag!==0&&n===2){var r=js(e);r!==0&&(t=r,n=so(e,r))}if(n===1)throw n=lr,Mt(e,0),at(e,t),ve(e,Y()),n;if(n===6)throw Error(w(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,jt(e,he,qe),ve(e,Y()),null}function el(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(gn=Y()+500,Ci&&Nt())}}function $t(e){ft!==null&&ft.tag===0&&!(z&6)&&cn();var t=z;z|=1;var n=Le.transition,r=A;try{if(Le.transition=null,A=1,e)return e()}finally{A=r,Le.transition=n,z=t,!(z&6)&&Nt()}}function tl(){we=rn.current,F(rn)}function Mt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Vd(n)),q!==null)for(n=q.return;n!==null;){var r=n;switch(Mo(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&si();break;case 3:mn(),F(ye),F(ue),Wo();break;case 5:Ho(r);break;case 4:mn();break;case 13:F(V);break;case 19:F(V);break;case 10:Fo(r.type._context);break;case 22:case 23:tl()}n=n.return}if(ee=e,q=e=wt(e.current,null),re=we=t,J=0,lr=null,Zo=Ri=Ut=0,he=Hn=null,Dt!==null){for(t=0;t<Dt.length;t++)if(n=Dt[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}Dt=null}return e}function Lc(e,t){do{var n=q;try{if(Bo(),Ur.current=hi,pi){for(var r=H.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}pi=!1}if(Ft=0,b=G=H=null,$n=!1,ir=0,Jo.current=null,n===null||n.return===null){J=1,lr=t,q=null;break}e:{var s=e,o=n.return,l=n,u=t;if(t=re,l.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var d=u,g=l,y=g.tag;if(!(g.mode&1)&&(y===0||y===11||y===15)){var m=g.alternate;m?(g.updateQueue=m.updateQueue,g.memoizedState=m.memoizedState,g.lanes=m.lanes):(g.updateQueue=null,g.memoizedState=null)}var S=iu(o);if(S!==null){S.flags&=-257,su(S,o,l,s,t),S.mode&1&&ru(s,d,t),t=S,u=d;var E=t.updateQueue;if(E===null){var N=new Set;N.add(u),t.updateQueue=N}else E.add(u);break e}else{if(!(t&1)){ru(s,d,t),nl();break e}u=Error(w(426))}}else if(U&&l.mode&1){var M=iu(o);if(M!==null){!(M.flags&65536)&&(M.flags|=256),su(M,o,l,s,t),Ao(yn(u,l));break e}}s=u=yn(u,l),J!==4&&(J=2),Hn===null?Hn=[s]:Hn.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var p=dc(s,u,t);Jl(s,p);break e;case 1:l=u;var a=s.type,f=s.stateNode;if(!(s.flags&128)&&(typeof a.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(gt===null||!gt.has(f)))){s.flags|=65536,t&=-t,s.lanes|=t;var c=pc(s,l,t);Jl(s,c);break e}}s=s.return}while(s!==null)}Oc(n)}catch(v){t=v,q===n&&n!==null&&(q=n=n.return);continue}break}while(!0)}function Rc(){var e=mi.current;return mi.current=hi,e===null?hi:e}function nl(){(J===0||J===3||J===2)&&(J=4),ee===null||!(Ut&268435455)&&!(Ri&268435455)||at(ee,re)}function vi(e,t){var n=z;z|=2;var r=Rc();(ee!==e||re!==t)&&(qe=null,Mt(e,t));do try{pp();break}catch(i){Lc(e,i)}while(!0);if(Bo(),z=n,mi.current=r,q!==null)throw Error(w(261));return ee=null,re=0,J}function pp(){for(;q!==null;)jc(q)}function hp(){for(;q!==null&&!Ff();)jc(q)}function jc(e){var t=zc(e.alternate,e,we);e.memoizedProps=e.pendingProps,t===null?Oc(e):q=t,Jo.current=null}function Oc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=lp(n,t),n!==null){n.flags&=32767,q=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{J=6,q=null;return}}else if(n=op(n,t,we),n!==null){q=n;return}if(t=t.sibling,t!==null){q=t;return}q=t=e}while(t!==null);J===0&&(J=5)}function jt(e,t,n){var r=A,i=Le.transition;try{Le.transition=null,A=1,mp(e,t,n,r)}finally{Le.transition=i,A=r}return null}function mp(e,t,n,r){do cn();while(ft!==null);if(z&6)throw Error(w(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(w(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Xf(e,s),e===ee&&(q=ee=null,re=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Rr||(Rr=!0,Mc(br,function(){return cn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Le.transition,Le.transition=null;var o=A;A=1;var l=z;z|=4,Jo.current=null,ap(e,n),Cc(n,e),Md(Is),ti=!!As,Is=As=null,e.current=n,cp(n),Uf(),z=l,A=o,Le.transition=s}else e.current=n;if(Rr&&(Rr=!1,ft=e,gi=i),s=e.pendingLanes,s===0&&(gt=null),Hf(n.stateNode),ve(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(yi)throw yi=!1,e=ro,ro=null,e;return gi&1&&e.tag!==0&&cn(),s=e.pendingLanes,s&1?e===io?Wn++:(Wn=0,io=e):Wn=0,Nt(),null}function cn(){if(ft!==null){var e=da(gi),t=Le.transition,n=A;try{if(Le.transition=null,A=16>e?16:e,ft===null)var r=!1;else{if(e=ft,ft=null,gi=0,z&6)throw Error(w(331));var i=z;for(z|=4,C=e.current;C!==null;){var s=C,o=s.child;if(C.flags&16){var l=s.deletions;if(l!==null){for(var u=0;u<l.length;u++){var d=l[u];for(C=d;C!==null;){var g=C;switch(g.tag){case 0:case 11:case 15:Vn(8,g,s)}var y=g.child;if(y!==null)y.return=g,C=y;else for(;C!==null;){g=C;var m=g.sibling,S=g.return;if(_c(g),g===d){C=null;break}if(m!==null){m.return=S,C=m;break}C=S}}}var E=s.alternate;if(E!==null){var N=E.child;if(N!==null){E.child=null;do{var M=N.sibling;N.sibling=null,N=M}while(N!==null)}}C=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,C=o;else e:for(;C!==null;){if(s=C,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Vn(9,s,s.return)}var p=s.sibling;if(p!==null){p.return=s.return,C=p;break e}C=s.return}}var a=e.current;for(C=a;C!==null;){o=C;var f=o.child;if(o.subtreeFlags&2064&&f!==null)f.return=o,C=f;else e:for(o=a;C!==null;){if(l=C,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Li(9,l)}}catch(v){Q(l,l.return,v)}if(l===o){C=null;break e}var c=l.sibling;if(c!==null){c.return=l.return,C=c;break e}C=l.return}}if(z=i,Nt(),We&&typeof We.onPostCommitFiberRoot=="function")try{We.onPostCommitFiberRoot(ki,e)}catch{}r=!0}return r}finally{A=n,Le.transition=t}}return!1}function vu(e,t,n){t=yn(n,t),t=dc(e,t,1),e=yt(e,t,1),t=ce(),e!==null&&(ar(e,1,t),ve(e,t))}function Q(e,t,n){if(e.tag===3)vu(e,e,n);else for(;t!==null;){if(t.tag===3){vu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(gt===null||!gt.has(r))){e=yn(n,e),e=pc(t,e,1),t=yt(t,e,1),e=ce(),t!==null&&(ar(t,1,e),ve(t,e));break}}t=t.return}}function yp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ce(),e.pingedLanes|=e.suspendedLanes&n,ee===e&&(re&n)===n&&(J===4||J===3&&(re&130023424)===re&&500>Y()-bo?Mt(e,0):Zo|=n),ve(e,t)}function Dc(e,t){t===0&&(e.mode&1?(t=xr,xr<<=1,!(xr&130023424)&&(xr=4194304)):t=1);var n=ce();e=tt(e,t),e!==null&&(ar(e,t,n),ve(e,n))}function gp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Dc(e,n)}function vp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(w(314))}r!==null&&r.delete(t),Dc(e,n)}var zc;zc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ye.current)me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return me=!1,sp(e,t,n);me=!!(e.flags&131072)}else me=!1,U&&t.flags&1048576&&Ba(t,ui,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vr(e,t),e=t.pendingProps;var i=dn(t,ue.current);an(t,n),i=Ko(null,t,r,e,i,n);var s=Yo();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ge(r)?(s=!0,oi(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,$o(t),i.updater=Pi,t.stateNode=i,i._reactInternals=t,Ks(t,r,e,n),t=Xs(null,t,r,!0,s,n)):(t.tag=0,U&&s&&zo(t),ae(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vr(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=xp(r),e=De(r,e),i){case 0:t=qs(null,t,r,e,n);break e;case 1:t=uu(null,t,r,e,n);break e;case 11:t=ou(null,t,r,e,n);break e;case 14:t=lu(null,t,r,De(r.type,e),n);break e}throw Error(w(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:De(r,i),qs(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:De(r,i),uu(e,t,r,i,n);case 3:e:{if(gc(t),e===null)throw Error(w(387));r=t.pendingProps,s=t.memoizedState,i=s.element,Wa(e,t),fi(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=yn(Error(w(423)),t),t=au(e,t,r,n,i);break e}else if(r!==i){i=yn(Error(w(424)),t),t=au(e,t,r,n,i);break e}else for(xe=mt(t.stateNode.containerInfo.firstChild),ke=t,U=!0,Ae=null,n=Va(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(pn(),r===i){t=nt(e,t,n);break e}ae(e,t,r,n)}t=t.child}return t;case 5:return Qa(t),e===null&&Hs(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,Bs(r,i)?o=null:s!==null&&Bs(r,s)&&(t.flags|=32),yc(e,t),ae(e,t,o,n),t.child;case 6:return e===null&&Hs(t),null;case 13:return vc(e,t,n);case 4:return Vo(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=hn(t,null,r,n):ae(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:De(r,i),ou(e,t,r,i,n);case 7:return ae(e,t,t.pendingProps,n),t.child;case 8:return ae(e,t,t.pendingProps.children,n),t.child;case 12:return ae(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,I(ai,r._currentValue),r._currentValue=o,s!==null)if(Fe(s.value,o)){if(s.children===i.children&&!ye.current){t=nt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){o=s.child;for(var u=l.firstContext;u!==null;){if(u.context===r){if(s.tag===1){u=Ze(-1,n&-n),u.tag=2;var d=s.updateQueue;if(d!==null){d=d.shared;var g=d.pending;g===null?u.next=u:(u.next=g.next,g.next=u),d.pending=u}}s.lanes|=n,u=s.alternate,u!==null&&(u.lanes|=n),Ws(s.return,n,t),l.lanes|=n;break}u=u.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(w(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Ws(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}ae(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,an(t,n),i=Re(i),r=r(i),t.flags|=1,ae(e,t,r,n),t.child;case 14:return r=t.type,i=De(r,t.pendingProps),i=De(r.type,i),lu(e,t,r,i,n);case 15:return hc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:De(r,i),Vr(e,t),t.tag=1,ge(r)?(e=!0,oi(t)):e=!1,an(t,n),fc(t,r,i),Ks(t,r,i,n),Xs(null,t,r,!0,e,n);case 19:return wc(e,t,n);case 22:return mc(e,t,n)}throw Error(w(156,t.tag))};function Mc(e,t){return ua(e,t)}function wp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Pe(e,t,n,r){return new wp(e,t,n,r)}function rl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function xp(e){if(typeof e=="function")return rl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===So)return 11;if(e===_o)return 14}return 2}function wt(e,t){var n=e.alternate;return n===null?(n=Pe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Qr(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")rl(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Yt:return At(n.children,i,s,t);case ko:o=8,i|=8;break;case ys:return e=Pe(12,n,t,i|2),e.elementType=ys,e.lanes=s,e;case gs:return e=Pe(13,n,t,i),e.elementType=gs,e.lanes=s,e;case vs:return e=Pe(19,n,t,i),e.elementType=vs,e.lanes=s,e;case Qu:return ji(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Hu:o=10;break e;case Wu:o=9;break e;case So:o=11;break e;case _o:o=14;break e;case ot:o=16,r=null;break e}throw Error(w(130,e==null?e:typeof e,""))}return t=Pe(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function At(e,t,n,r){return e=Pe(7,e,r,t),e.lanes=n,e}function ji(e,t,n,r){return e=Pe(22,e,r,t),e.elementType=Qu,e.lanes=n,e.stateNode={isHidden:!1},e}function as(e,t,n){return e=Pe(6,e,null,t),e.lanes=n,e}function cs(e,t,n){return t=Pe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function kp(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Wi(0),this.expirationTimes=Wi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Wi(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function il(e,t,n,r,i,s,o,l,u){return e=new kp(e,t,n,l,u),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Pe(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},$o(s),e}function Sp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Kt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Ac(e){if(!e)return St;e=e._reactInternals;e:{if(Ht(e)!==e||e.tag!==1)throw Error(w(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ge(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(w(171))}if(e.tag===1){var n=e.type;if(ge(n))return Aa(e,n,t)}return t}function Ic(e,t,n,r,i,s,o,l,u){return e=il(n,r,!0,e,i,s,o,l,u),e.context=Ac(null),n=e.current,r=ce(),i=vt(n),s=Ze(r,i),s.callback=t??null,yt(n,s,i),e.current.lanes=i,ar(e,i,r),ve(e,r),e}function Oi(e,t,n,r){var i=t.current,s=ce(),o=vt(i);return n=Ac(n),t.context===null?t.context=n:t.pendingContext=n,t=Ze(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=yt(i,t,o),e!==null&&(Be(e,i,o,s),Fr(e,i,o)),o}function wi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function wu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function sl(e,t){wu(e,t),(e=e.alternate)&&wu(e,t)}function _p(){return null}var Bc=typeof reportError=="function"?reportError:function(e){console.error(e)};function ol(e){this._internalRoot=e}Di.prototype.render=ol.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(w(409));Oi(e,t,null,null)};Di.prototype.unmount=ol.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;$t(function(){Oi(null,e,null,null)}),t[et]=null}};function Di(e){this._internalRoot=e}Di.prototype.unstable_scheduleHydration=function(e){if(e){var t=ma();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ut.length&&t!==0&&t<ut[n].priority;n++);ut.splice(n,0,e),n===0&&ga(e)}};function ll(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function zi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function xu(){}function Ep(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var d=wi(o);s.call(d)}}var o=Ic(t,r,e,0,null,!1,!1,"",xu);return e._reactRootContainer=o,e[et]=o.current,bn(e.nodeType===8?e.parentNode:e),$t(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var d=wi(u);l.call(d)}}var u=il(e,0,!1,null,null,!1,!1,"",xu);return e._reactRootContainer=u,e[et]=u.current,bn(e.nodeType===8?e.parentNode:e),$t(function(){Oi(t,u,n,r)}),u}function Mi(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var l=i;i=function(){var u=wi(o);l.call(u)}}Oi(t,o,e,i)}else o=Ep(n,t,e,i,r);return wi(o)}pa=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Dn(t.pendingLanes);n!==0&&(Co(t,n|1),ve(t,Y()),!(z&6)&&(gn=Y()+500,Nt()))}break;case 13:$t(function(){var r=tt(e,1);if(r!==null){var i=ce();Be(r,e,1,i)}}),sl(e,1)}};To=function(e){if(e.tag===13){var t=tt(e,134217728);if(t!==null){var n=ce();Be(t,e,134217728,n)}sl(e,134217728)}};ha=function(e){if(e.tag===13){var t=vt(e),n=tt(e,t);if(n!==null){var r=ce();Be(n,e,t,r)}sl(e,t)}};ma=function(){return A};ya=function(e,t){var n=A;try{return A=e,t()}finally{A=n}};Ps=function(e,t,n){switch(t){case"input":if(ks(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ni(r);if(!i)throw Error(w(90));Yu(r),ks(r,i)}}}break;case"textarea":Xu(e,n);break;case"select":t=n.value,t!=null&&sn(e,!!n.multiple,t,!1)}};na=el;ra=$t;var Np={usingClientEntryPoint:!1,Events:[fr,Jt,Ni,ea,ta,el]},Ln={findFiberByHostInstance:Ot,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Cp={bundleType:Ln.bundleType,version:Ln.version,rendererPackageName:Ln.rendererPackageName,rendererConfig:Ln.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:rt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=oa(e),e===null?null:e.stateNode},findFiberByHostInstance:Ln.findFiberByHostInstance||_p,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var jr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!jr.isDisabled&&jr.supportsFiber)try{ki=jr.inject(Cp),We=jr}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Np;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ll(t))throw Error(w(200));return Sp(e,t,null,n)};_e.createRoot=function(e,t){if(!ll(e))throw Error(w(299));var n=!1,r="",i=Bc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=il(e,1,!1,null,null,n,!1,r,i),e[et]=t.current,bn(e.nodeType===8?e.parentNode:e),new ol(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(w(188)):(e=Object.keys(e).join(","),Error(w(268,e)));return e=oa(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return $t(e)};_e.hydrate=function(e,t,n){if(!zi(t))throw Error(w(200));return Mi(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!ll(e))throw Error(w(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=Bc;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Ic(t,null,e,1,n??null,i,!1,s,o),e[et]=t.current,bn(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Di(t)};_e.render=function(e,t,n){if(!zi(t))throw Error(w(200));return Mi(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!zi(e))throw Error(w(40));return e._reactRootContainer?($t(function(){Mi(null,null,e,!1,function(){e._reactRootContainer=null,e[et]=null})}),!0):!1};_e.unstable_batchedUpdates=el;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!zi(n))throw Error(w(200));if(e==null||e._reactInternals===void 0)throw Error(w(38));return Mi(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function Fc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Fc)}catch(e){console.error(e)}}Fc(),Fu.exports=_e;var Tp=Fu.exports,ku=Tp;hs.createRoot=ku.createRoot,hs.hydrateRoot=ku.hydrateRoot;const Ye=Object.create(null);Ye.open="0";Ye.close="1";Ye.ping="2";Ye.pong="3";Ye.message="4";Ye.upgrade="5";Ye.noop="6";const Kr=Object.create(null);Object.keys(Ye).forEach(e=>{Kr[Ye[e]]=e});const lo={type:"error",data:"parser error"},Uc=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",$c=typeof ArrayBuffer=="function",Vc=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,ul=({type:e,data:t},n,r)=>Uc&&t instanceof Blob?n?r(t):Su(t,r):$c&&(t instanceof ArrayBuffer||Vc(t))?n?r(t):Su(new Blob([t]),r):r(Ye[e]+(t||"")),Su=(e,t)=>{const n=new FileReader;return n.onload=function(){const r=n.result.split(",")[1];t("b"+(r||""))},n.readAsDataURL(e)};function _u(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let fs;function Pp(e,t){if(Uc&&e.data instanceof Blob)return e.data.arrayBuffer().then(_u).then(t);if($c&&(e.data instanceof ArrayBuffer||Vc(e.data)))return t(_u(e.data));ul(e,!1,n=>{fs||(fs=new TextEncoder),t(fs.encode(n))})}const Eu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Mn=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Eu.length;e++)Mn[Eu.charCodeAt(e)]=e;const Lp=e=>{let t=e.length*.75,n=e.length,r,i=0,s,o,l,u;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const d=new ArrayBuffer(t),g=new Uint8Array(d);for(r=0;r<n;r+=4)s=Mn[e.charCodeAt(r)],o=Mn[e.charCodeAt(r+1)],l=Mn[e.charCodeAt(r+2)],u=Mn[e.charCodeAt(r+3)],g[i++]=s<<2|o>>4,g[i++]=(o&15)<<4|l>>2,g[i++]=(l&3)<<6|u&63;return d},Rp=typeof ArrayBuffer=="function",al=(e,t)=>{if(typeof e!="string")return{type:"message",data:Hc(e,t)};const n=e.charAt(0);return n==="b"?{type:"message",data:jp(e.substring(1),t)}:Kr[n]?e.length>1?{type:Kr[n],data:e.substring(1)}:{type:Kr[n]}:lo},jp=(e,t)=>{if(Rp){const n=Lp(e);return Hc(n,t)}else return{base64:!0,data:e}},Hc=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},Wc="",Op=(e,t)=>{const n=e.length,r=new Array(n);let i=0;e.forEach((s,o)=>{ul(s,!1,l=>{r[o]=l,++i===n&&t(r.join(Wc))})})},Dp=(e,t)=>{const n=e.split(Wc),r=[];for(let i=0;i<n.length;i++){const s=al(n[i],t);if(r.push(s),s.type==="error")break}return r};function zp(){return new TransformStream({transform(e,t){Pp(e,n=>{const r=n.length;let i;if(r<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,r);else if(r<65536){i=new Uint8Array(3);const s=new DataView(i.buffer);s.setUint8(0,126),s.setUint16(1,r)}else{i=new Uint8Array(9);const s=new DataView(i.buffer);s.setUint8(0,127),s.setBigUint64(1,BigInt(r))}e.data&&typeof e.data!="string"&&(i[0]|=128),t.enqueue(i),t.enqueue(n)})}})}let ds;function Or(e){return e.reduce((t,n)=>t+n.length,0)}function Dr(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let i=0;i<t;i++)n[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function Mp(e,t){ds||(ds=new TextDecoder);const n=[];let r=0,i=-1,s=!1;return new TransformStream({transform(o,l){for(n.push(o);;){if(r===0){if(Or(n)<1)break;const u=Dr(n,1);s=(u[0]&128)===128,i=u[0]&127,i<126?r=3:i===126?r=1:r=2}else if(r===1){if(Or(n)<2)break;const u=Dr(n,2);i=new DataView(u.buffer,u.byteOffset,u.length).getUint16(0),r=3}else if(r===2){if(Or(n)<8)break;const u=Dr(n,8),d=new DataView(u.buffer,u.byteOffset,u.length),g=d.getUint32(0);if(g>Math.pow(2,21)-1){l.enqueue(lo);break}i=g*Math.pow(2,32)+d.getUint32(4),r=3}else{if(Or(n)<i)break;const u=Dr(n,i);l.enqueue(al(s?u:ds.decode(u),t)),r=0}if(i===0||i>e){l.enqueue(lo);break}}}})}const Qc=4;function X(e){if(e)return Ap(e)}function Ap(e){for(var t in X.prototype)e[t]=X.prototype[t];return e}X.prototype.on=X.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};X.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this};X.prototype.off=X.prototype.removeListener=X.prototype.removeAllListeners=X.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var n=this._callbacks["$"+e];if(!n)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var r,i=0;i<n.length;i++)if(r=n[i],r===t||r.fn===t){n.splice(i,1);break}return n.length===0&&delete this._callbacks["$"+e],this};X.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){n=n.slice(0);for(var r=0,i=n.length;r<i;++r)n[r].apply(this,t)}return this};X.prototype.emitReserved=X.prototype.emit;X.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};X.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Ai=typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,n)=>n(t,0),Te=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),Ip="arraybuffer";function Kc(e,...t){return t.reduce((n,r)=>(e.hasOwnProperty(r)&&(n[r]=e[r]),n),{})}const Bp=Te.setTimeout,Fp=Te.clearTimeout;function Ii(e,t){t.useNativeTimers?(e.setTimeoutFn=Bp.bind(Te),e.clearTimeoutFn=Fp.bind(Te)):(e.setTimeoutFn=Te.setTimeout.bind(Te),e.clearTimeoutFn=Te.clearTimeout.bind(Te))}const Up=1.33;function $p(e){return typeof e=="string"?Vp(e):Math.ceil((e.byteLength||e.size)*Up)}function Vp(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}function Yc(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function Hp(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}function Wp(e){let t={},n=e.split("&");for(let r=0,i=n.length;r<i;r++){let s=n[r].split("=");t[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return t}class Qp extends Error{constructor(t,n,r){super(t),this.description=n,this.context=r,this.type="TransportError"}}class cl extends X{constructor(t){super(),this.writable=!1,Ii(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,n,r){return super.emitReserved("error",new Qp(t,n,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const n=al(t,this.socket.binaryType);this.onPacket(n)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,n={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(n)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const n=Hp(t);return n.length?"?"+n:""}}class Kp extends cl{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const n=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let r=0;this._polling&&(r++,this.once("pollComplete",function(){--r||n()})),this.writable||(r++,this.once("drain",function(){--r||n()}))}else n()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const n=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};Dp(t,this.socket.binaryType).forEach(n),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,Op(t,n=>{this.doWrite(n,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",n=this.query||{};return this.opts.timestampRequests!==!1&&(n[this.opts.timestampParam]=Yc()),!this.supportsBinary&&!n.sid&&(n.b64=1),this.createUri(t,n)}}let qc=!1;try{qc=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const Yp=qc;function qp(){}class Xp extends Kp{constructor(t){if(super(t),typeof location<"u"){const n=location.protocol==="https:";let r=location.port;r||(r=n?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,n){const r=this.request({method:"POST",data:t});r.on("success",n),r.on("error",(i,s)=>{this.onError("xhr post error",i,s)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(n,r)=>{this.onError("xhr poll error",n,r)}),this.pollXhr=t}}class Ke extends X{constructor(t,n,r){super(),this.createRequest=t,Ii(this,r),this._opts=r,this._method=r.method||"GET",this._uri=n,this._data=r.data!==void 0?r.data:null,this._create()}_create(){var t;const n=Kc(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");n.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(n);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let i in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(i)&&r.setRequestHeader(i,this._opts.extraHeaders[i])}}catch{}if(this._method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{r.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var i;r.readyState===3&&((i=this._opts.cookieJar)===null||i===void 0||i.parseCookies(r.getResponseHeader("set-cookie"))),r.readyState===4&&(r.status===200||r.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof r.status=="number"?r.status:0)},0))},r.send(this._data)}catch(i){this.setTimeoutFn(()=>{this._onError(i)},0);return}typeof document<"u"&&(this._index=Ke.requestsCount++,Ke.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=qp,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete Ke.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}Ke.requestsCount=0;Ke.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Nu);else if(typeof addEventListener=="function"){const e="onpagehide"in Te?"pagehide":"unload";addEventListener(e,Nu,!1)}}function Nu(){for(let e in Ke.requests)Ke.requests.hasOwnProperty(e)&&Ke.requests[e].abort()}const Gp=function(){const e=Xc({xdomain:!1});return e&&e.responseType!==null}();class Jp extends Xp{constructor(t){super(t);const n=t&&t.forceBase64;this.supportsBinary=Gp&&!n}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new Ke(Xc,this.uri(),t)}}function Xc(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||Yp))return new XMLHttpRequest}catch{}if(!t)try{return new Te[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Gc=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Zp extends cl{get name(){return"websocket"}doOpen(){const t=this.uri(),n=this.opts.protocols,r=Gc?{}:Kc(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,n,r)}catch(i){return this.emitReserved("error",i)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],i=n===t.length-1;ul(r,this.supportsBinary,s=>{try{this.doWrite(r,s)}catch{}i&&Ai(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",n=this.query||{};return this.opts.timestampRequests&&(n[this.opts.timestampParam]=Yc()),this.supportsBinary||(n.b64=1),this.createUri(t,n)}}const ps=Te.WebSocket||Te.MozWebSocket;class bp extends Zp{createSocket(t,n,r){return Gc?new ps(t,n,r):n?new ps(t,n):new ps(t)}doWrite(t,n){this.ws.send(n)}}class eh extends cl{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const n=Mp(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(n).getReader(),i=zp();i.readable.pipeTo(t.writable),this._writer=i.writable.getWriter();const s=()=>{r.read().then(({done:l,value:u})=>{l||(this.onPacket(u),s())}).catch(l=>{})};s();const o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this._writer.write(o).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],i=n===t.length-1;this._writer.write(r).then(()=>{i&&Ai(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const th={websocket:bp,webtransport:eh,polling:Jp},nh=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,rh=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function uo(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");n!=-1&&r!=-1&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let i=nh.exec(e||""),s={},o=14;for(;o--;)s[rh[o]]=i[o]||"";return n!=-1&&r!=-1&&(s.source=t,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=ih(s,s.path),s.queryKey=sh(s,s.query),s}function ih(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&r.splice(0,1),t.slice(-1)=="/"&&r.splice(r.length-1,1),r}function sh(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,i,s){i&&(n[i]=s)}),n}const ao=typeof addEventListener=="function"&&typeof removeEventListener=="function",Yr=[];ao&&addEventListener("offline",()=>{Yr.forEach(e=>e())},!1);class xt extends X{constructor(t,n){if(super(),this.binaryType=Ip,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(n=t,t=null),t){const r=uo(t);n.hostname=r.host,n.secure=r.protocol==="https"||r.protocol==="wss",n.port=r.port,r.query&&(n.query=r.query)}else n.host&&(n.hostname=uo(n.host).host);Ii(this,n),this.secure=n.secure!=null?n.secure:typeof location<"u"&&location.protocol==="https:",n.hostname&&!n.port&&(n.port=this.secure?"443":"80"),this.hostname=n.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=n.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},n.transports.forEach(r=>{const i=r.prototype.name;this.transports.push(i),this._transportsByName[i]=r}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},n),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Wp(this.opts.query)),ao&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Yr.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const n=Object.assign({},this.opts.query);n.EIO=Qc,n.transport=t,this.id&&(n.sid=this.id);const r=Object.assign({},this.opts,{query:n,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&xt.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const n=this.createTransport(t);n.open(),this.setTransport(n)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",n=>this._onClose("transport close",n))}onOpen(){this.readyState="open",xt.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const n=new Error("server error");n.code=t.data,this._onError(n);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let n=1;for(let r=0;r<this.writeBuffer.length;r++){const i=this.writeBuffer[r].data;if(i&&(n+=$p(i)),r>0&&n>this._maxPayload)return this.writeBuffer.slice(0,r);n+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,Ai(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,n,r){return this._sendPacket("message",t,n,r),this}send(t,n,r){return this._sendPacket("message",t,n,r),this}_sendPacket(t,n,r,i){if(typeof n=="function"&&(i=n,n=void 0),typeof r=="function"&&(i=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;const s={type:t,data:n,options:r};this.emitReserved("packetCreate",s),this.writeBuffer.push(s),i&&this.once("flush",i),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},n=()=>{this.off("upgrade",n),this.off("upgradeError",n),t()},r=()=>{this.once("upgrade",n),this.once("upgradeError",n)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(xt.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,n){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),ao&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const r=Yr.indexOf(this._offlineEventListener);r!==-1&&Yr.splice(r,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,n),this.writeBuffer=[],this._prevBufferLen=0}}}xt.protocol=Qc;class oh extends xt{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let n=this.createTransport(t),r=!1;xt.priorWebsocketSuccess=!1;const i=()=>{r||(n.send([{type:"ping",data:"probe"}]),n.once("packet",y=>{if(!r)if(y.type==="pong"&&y.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",n),!n)return;xt.priorWebsocketSuccess=n.name==="websocket",this.transport.pause(()=>{r||this.readyState!=="closed"&&(g(),this.setTransport(n),n.send([{type:"upgrade"}]),this.emitReserved("upgrade",n),n=null,this.upgrading=!1,this.flush())})}else{const m=new Error("probe error");m.transport=n.name,this.emitReserved("upgradeError",m)}}))};function s(){r||(r=!0,g(),n.close(),n=null)}const o=y=>{const m=new Error("probe error: "+y);m.transport=n.name,s(),this.emitReserved("upgradeError",m)};function l(){o("transport closed")}function u(){o("socket closed")}function d(y){n&&y.name!==n.name&&s()}const g=()=>{n.removeListener("open",i),n.removeListener("error",o),n.removeListener("close",l),this.off("close",u),this.off("upgrading",d)};n.once("open",i),n.once("error",o),n.once("close",l),this.once("close",u),this.once("upgrading",d),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{r||n.open()},200):n.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const n=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&n.push(t[r]);return n}}let lh=class extends oh{constructor(t,n={}){const r=typeof t=="object"?t:n;(!r.transports||r.transports&&typeof r.transports[0]=="string")&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(i=>th[i]).filter(i=>!!i)),super(t,r)}};function uh(e,t="",n){let r=e;n=n||typeof location<"u"&&location,e==null&&(e=n.protocol+"//"+n.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=n.protocol+e:e=n.host+e),/^(https?|wss?):\/\//.test(e)||(typeof n<"u"?e=n.protocol+"//"+e:e="https://"+e),r=uo(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const s=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+s+":"+r.port+t,r.href=r.protocol+"://"+s+(n&&n.port===r.port?"":":"+r.port),r}const ah=typeof ArrayBuffer=="function",ch=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,Jc=Object.prototype.toString,fh=typeof Blob=="function"||typeof Blob<"u"&&Jc.call(Blob)==="[object BlobConstructor]",dh=typeof File=="function"||typeof File<"u"&&Jc.call(File)==="[object FileConstructor]";function fl(e){return ah&&(e instanceof ArrayBuffer||ch(e))||fh&&e instanceof Blob||dh&&e instanceof File}function qr(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let n=0,r=e.length;n<r;n++)if(qr(e[n]))return!0;return!1}if(fl(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return qr(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&qr(e[n]))return!0;return!1}function ph(e){const t=[],n=e.data,r=e;return r.data=co(n,t),r.attachments=t.length,{packet:r,buffers:t}}function co(e,t){if(!e)return e;if(fl(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}else if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=co(e[r],t);return n}else if(typeof e=="object"&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=co(e[r],t));return n}return e}function hh(e,t){return e.data=fo(e.data,t),delete e.attachments,e}function fo(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=fo(e[n],t);else if(typeof e=="object")for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=fo(e[n],t));return e}const mh=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],yh=5;var D;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(D||(D={}));class gh{constructor(t){this.replacer=t}encode(t){return(t.type===D.EVENT||t.type===D.ACK)&&qr(t)?this.encodeAsBinary({type:t.type===D.EVENT?D.BINARY_EVENT:D.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let n=""+t.type;return(t.type===D.BINARY_EVENT||t.type===D.BINARY_ACK)&&(n+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(n+=t.nsp+","),t.id!=null&&(n+=t.id),t.data!=null&&(n+=JSON.stringify(t.data,this.replacer)),n}encodeAsBinary(t){const n=ph(t),r=this.encodeAsString(n.packet),i=n.buffers;return i.unshift(r),i}}function Cu(e){return Object.prototype.toString.call(e)==="[object Object]"}class dl extends X{constructor(t){super(),this.reviver=t}add(t){let n;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");n=this.decodeString(t);const r=n.type===D.BINARY_EVENT;r||n.type===D.BINARY_ACK?(n.type=r?D.EVENT:D.ACK,this.reconstructor=new vh(n),n.attachments===0&&super.emitReserved("decoded",n)):super.emitReserved("decoded",n)}else if(fl(t)||t.base64)if(this.reconstructor)n=this.reconstructor.takeBinaryData(t),n&&(this.reconstructor=null,super.emitReserved("decoded",n));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let n=0;const r={type:Number(t.charAt(0))};if(D[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===D.BINARY_EVENT||r.type===D.BINARY_ACK){const s=n+1;for(;t.charAt(++n)!=="-"&&n!=t.length;);const o=t.substring(s,n);if(o!=Number(o)||t.charAt(n)!=="-")throw new Error("Illegal attachments");r.attachments=Number(o)}if(t.charAt(n+1)==="/"){const s=n+1;for(;++n&&!(t.charAt(n)===","||n===t.length););r.nsp=t.substring(s,n)}else r.nsp="/";const i=t.charAt(n+1);if(i!==""&&Number(i)==i){const s=n+1;for(;++n;){const o=t.charAt(n);if(o==null||Number(o)!=o){--n;break}if(n===t.length)break}r.id=Number(t.substring(s,n+1))}if(t.charAt(++n)){const s=this.tryParse(t.substr(n));if(dl.isPayloadValid(r.type,s))r.data=s;else throw new Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,n){switch(t){case D.CONNECT:return Cu(n);case D.DISCONNECT:return n===void 0;case D.CONNECT_ERROR:return typeof n=="string"||Cu(n);case D.EVENT:case D.BINARY_EVENT:return Array.isArray(n)&&(typeof n[0]=="number"||typeof n[0]=="string"&&mh.indexOf(n[0])===-1);case D.ACK:case D.BINARY_ACK:return Array.isArray(n)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class vh{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const n=hh(this.reconPack,this.buffers);return this.finishedReconstruction(),n}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const wh=Object.freeze(Object.defineProperty({__proto__:null,Decoder:dl,Encoder:gh,get PacketType(){return D},protocol:yh},Symbol.toStringTag,{value:"Module"}));function Me(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const xh=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Zc extends X{constructor(t,n,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=n,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[Me(t,"open",this.onopen.bind(this)),Me(t,"packet",this.onpacket.bind(this)),Me(t,"error",this.onerror.bind(this)),Me(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...n){var r,i,s;if(xh.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(n.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(n),this;const o={type:D.EVENT,data:n};if(o.options={},o.options.compress=this.flags.compress!==!1,typeof n[n.length-1]=="function"){const g=this.ids++,y=n.pop();this._registerAckCallback(g,y),o.id=g}const l=(i=(r=this.io.engine)===null||r===void 0?void 0:r.transport)===null||i===void 0?void 0:i.writable,u=this.connected&&!(!((s=this.io.engine)===null||s===void 0)&&s._hasPingExpired());return this.flags.volatile&&!l||(u?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(t,n){var r;const i=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(i===void 0){this.acks[t]=n;return}const s=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let l=0;l<this.sendBuffer.length;l++)this.sendBuffer[l].id===t&&this.sendBuffer.splice(l,1);n.call(this,new Error("operation has timed out"))},i),o=(...l)=>{this.io.clearTimeoutFn(s),n.apply(this,l)};o.withError=!0,this.acks[t]=o}emitWithAck(t,...n){return new Promise((r,i)=>{const s=(o,l)=>o?i(o):r(l);s.withError=!0,n.push(s),this.emit(t,...n)})}_addToQueue(t){let n;typeof t[t.length-1]=="function"&&(n=t.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((i,...s)=>r!==this._queue[0]?void 0:(i!==null?r.tryCount>this._opts.retries&&(this._queue.shift(),n&&n(i)):(this._queue.shift(),n&&n(null,...s)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const n=this._queue[0];n.pending&&!t||(n.pending=!0,n.tryCount++,this.flags=n.flags,this.emit.apply(this,n.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:D.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,n){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,n),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(r=>String(r.id)===t)){const r=this.acks[t];delete this.acks[t],r.withError&&r.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case D.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case D.EVENT:case D.BINARY_EVENT:this.onevent(t);break;case D.ACK:case D.BINARY_ACK:this.onack(t);break;case D.DISCONNECT:this.ondisconnect();break;case D.CONNECT_ERROR:this.destroy();const r=new Error(t.data.message);r.data=t.data.data,this.emitReserved("connect_error",r);break}}onevent(t){const n=t.data||[];t.id!=null&&n.push(this.ack(t.id)),this.connected?this.emitEvent(n):this.receiveBuffer.push(Object.freeze(n))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const n=this._anyListeners.slice();for(const r of n)r.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const n=this;let r=!1;return function(...i){r||(r=!0,n.packet({type:D.ACK,id:t,data:i}))}}onack(t){const n=this.acks[t.id];typeof n=="function"&&(delete this.acks[t.id],n.withError&&t.data.unshift(null),n.apply(this,t.data))}onconnect(t,n){this.id=t,this.recovered=n&&this._pid===n,this._pid=n,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:D.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const n=this._anyListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const n=this._anyOutgoingListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const n=this._anyOutgoingListeners.slice();for(const r of n)r.apply(this,t.data)}}}function kn(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}kn.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=Math.floor(t*10)&1?e+n:e-n}return Math.min(e,this.max)|0};kn.prototype.reset=function(){this.attempts=0};kn.prototype.setMin=function(e){this.ms=e};kn.prototype.setMax=function(e){this.max=e};kn.prototype.setJitter=function(e){this.jitter=e};class po extends X{constructor(t,n){var r;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(n=t,t=void 0),n=n||{},n.path=n.path||"/socket.io",this.opts=n,Ii(this,n),this.reconnection(n.reconnection!==!1),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor((r=n.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new kn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(n.timeout==null?2e4:n.timeout),this._readyState="closed",this.uri=t;const i=n.parser||wh;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=n.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var n;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(n=this.backoff)===null||n===void 0||n.setMin(t),this)}randomizationFactor(t){var n;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(n=this.backoff)===null||n===void 0||n.setJitter(t),this)}reconnectionDelayMax(t){var n;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(n=this.backoff)===null||n===void 0||n.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new lh(this.uri,this.opts);const n=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;const i=Me(n,"open",function(){r.onopen(),t&&t()}),s=l=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",l),t?t(l):this.maybeReconnectOnOpen()},o=Me(n,"error",s);if(this._timeout!==!1){const l=this._timeout,u=this.setTimeoutFn(()=>{i(),s(new Error("timeout")),n.close()},l);this.opts.autoUnref&&u.unref(),this.subs.push(()=>{this.clearTimeoutFn(u)})}return this.subs.push(i),this.subs.push(o),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(Me(t,"ping",this.onping.bind(this)),Me(t,"data",this.ondata.bind(this)),Me(t,"error",this.onerror.bind(this)),Me(t,"close",this.onclose.bind(this)),Me(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(n){this.onclose("parse error",n)}}ondecoded(t){Ai(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,n){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new Zc(this,t,n),this.nsps[t]=r),r}_destroy(t){const n=Object.keys(this.nsps);for(const r of n)if(this.nsps[r].active)return;this._close()}_packet(t){const n=this.encoder.encode(t);for(let r=0;r<n.length;r++)this.engine.write(n[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,n){var r;this.cleanup(),(r=this.engine)===null||r===void 0||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,n),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const n=this.backoff.duration();this._reconnecting=!0;const r=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(i=>{i?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",i)):t.onreconnect()}))},n);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const Rn={};function Xr(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const n=uh(e,t.path||"/socket.io"),r=n.source,i=n.id,s=n.path,o=Rn[i]&&s in Rn[i].nsps,l=t.forceNew||t["force new connection"]||t.multiplex===!1||o;let u;return l?u=new po(r,t):(Rn[i]||(Rn[i]=new po(r,t)),u=Rn[i]),n.query&&!t.query&&(t.query=n.queryKey),u.socket(n.path,t)}Object.assign(Xr,{Manager:po,Socket:Zc,io:Xr,connect:Xr});const kh=({patient:e,onClick:t})=>{const n=()=>{switch(e.status){case"emergency":return"border-red-500 bg-gradient-to-br from-gray-800 to-red-900/50";case"symptoms":return"border-yellow-500 bg-gradient-to-br from-gray-800 to-yellow-900/50";case"stable":return"border-green-500 bg-gradient-to-br from-gray-800 to-green-900/50";default:return"border-blue-500 bg-gradient-to-br from-gray-800 to-blue-900/50"}},r=()=>{if(!e.lastMessage)return"No activity";const s=new Date-new Date(e.lastMessage),o=Math.floor(s/(1e3*60*60));return o<1?"Just now":o<24?`${o}h ago`:`${Math.floor(o/24)}d ago`},i=()=>{switch(e.status){case"emergency":return"🚨";case"symptoms":return"🔍";case"stable":return"💊";default:return"👤"}};return h.jsx("div",{onClick:()=>t(e),className:`${n()} cursor-pointer rounded-2xl border-2 shadow-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl overflow-hidden backdrop-blur-sm`,children:h.jsxs("div",{className:"p-6 h-full flex flex-col min-h-[280px]",children:[h.jsxs("div",{className:"flex justify-between items-start mb-4",children:[h.jsxs("div",{className:"flex items-center space-x-4",children:[h.jsxs("div",{className:"relative",children:[h.jsx("img",{src:e.avatar,alt:e.name,className:"w-16 h-16 rounded-full border-2 border-gray-600"}),h.jsx("span",{className:"absolute -bottom-1 -right-1 text-2xl",children:i()})]}),h.jsxs("div",{children:[h.jsx("h3",{className:"text-xl font-bold text-white mb-1",children:e.name}),h.jsxs("p",{className:"text-sm text-gray-300",children:["ID: ",e.id]})]})]}),e.status==="emergency"&&h.jsx("span",{className:"bg-red-600 text-white px-3 py-1 rounded-full text-xs font-semibold animate-pulse",children:"URGENT"})]}),h.jsxs("div",{className:"flex-1 space-y-3",children:[h.jsx("div",{className:"bg-gray-800/50 rounded-lg p-3",children:h.jsxs("div",{className:"flex items-center justify-between text-sm",children:[h.jsx("span",{className:"text-gray-400",children:"Messages"}),h.jsx("span",{className:"text-white font-semibold",children:e.messageCount})]})}),h.jsx("div",{className:"bg-gray-800/50 rounded-lg p-3",children:h.jsxs("div",{className:"flex items-center justify-between text-sm",children:[h.jsx("span",{className:"text-gray-400",children:"Status"}),h.jsx("span",{className:`font-semibold capitalize ${e.status==="emergency"?"text-red-400":e.status==="symptoms"?"text-yellow-400":e.status==="stable"?"text-green-400":"text-blue-400"}`,children:e.status})]})})]}),h.jsxs("div",{className:"flex justify-between items-center mt-4 text-xs text-gray-400 border-t border-gray-700 pt-3",children:[h.jsxs("span",{children:["Last: ",r()]}),h.jsxs("span",{className:"flex items-center space-x-1",children:[h.jsx("span",{children:"👁️"}),h.jsx("span",{children:"View"})]})]})]})})},Sh=({patient:e,messages:t,onBack:n,onViewGraph:r})=>{const[i,s]=ne.useState(new Date),[o,l]=ne.useState(null),u=(c,v)=>new Date(c,v+1,0).getDate(),d=(c,v)=>new Date(c,v,1).getDay(),g=()=>{const c=i.getFullYear(),v=i.getMonth(),x=u(c,v),k=d(c,v),_=[];for(let j=0;j<k;j++)_.push({day:null,isEmpty:!0});for(let j=1;j<=x;j++){const P=new Date(c,v,j),$=P.toISOString().split("T")[0],Ct=t.some(Ue=>{const pe=new Date(Ue.timestamp);return pe.getFullYear()===c&&pe.getMonth()===v&&pe.getDate()===j}),Tt=t.filter(Ue=>{const pe=new Date(Ue.timestamp);return pe.getFullYear()===c&&pe.getMonth()===v&&pe.getDate()===j}),it={symptom:0,medication:0,emergency:0,message:0};Tt.forEach(Ue=>{var pe,Wt;Ue.subtype==="symptom"?it.symptom++:Ue.subtype==="recipe"?it.medication++:((pe=Ue.payload)==null?void 0:pe.severity)==="critical"||((Wt=Ue.payload)==null?void 0:Wt.severity)==="high"?it.emergency++:it.message++}),_.push({day:j,date:P,dateKey:$,hasEvents:Ct,eventCounts:it,isToday:new Date().toDateString()===P.toDateString(),isSelected:o&&o.toDateString()===P.toDateString()})}return _},y=()=>{const c={month:"long",year:"numeric"};return i.toLocaleDateString(void 0,c)},m=()=>{s(new Date(i.getFullYear(),i.getMonth()-1,1))},S=()=>{s(new Date(i.getFullYear(),i.getMonth()+1,1))},E=c=>{if(!c.day)return;l(c.date),t.filter(x=>new Date(x.timestamp).toDateString()===c.date.toDateString()).sort((x,k)=>new Date(x.timestamp)-new Date(k.timestamp))},N=()=>o?t.filter(c=>new Date(c.timestamp).toDateString()===o.toDateString()).sort((c,v)=>new Date(c.timestamp)-new Date(v.timestamp)):[],M=c=>{var v,x;if(((v=c.payload)==null?void 0:v.severity)==="critical"||((x=c.payload)==null?void 0:x.severity)==="high")return"🚨";switch(c.subtype){case"symptom":return"🩺";case"recipe":return"💊";case"image":return"📷";default:return"💬"}},p=c=>new Date(c).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),a=g(),f=N();return h.jsxs("div",{className:"flex flex-col h-full bg-gray-900 text-white",children:[h.jsx("div",{className:"p-6 border-b border-gray-700",children:h.jsxs("div",{className:"flex justify-between items-center",children:[h.jsxs("div",{className:"flex items-center space-x-4",children:[h.jsx("button",{onClick:n,className:"p-2 rounded-full hover:bg-gray-700 transition-colors",children:h.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),h.jsxs("div",{children:[h.jsxs("h2",{className:"text-2xl font-bold text-white",children:[e.name," - ",y()]}),h.jsx("p",{className:"text-gray-400 text-sm",children:"Patient interactions timeline"})]})]}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx("button",{onClick:r,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",children:"📊 Graph View"}),h.jsx("button",{onClick:m,className:"p-2 rounded-full hover:bg-gray-700 transition-colors",children:h.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),h.jsx("button",{onClick:S,className:"p-2 rounded-full hover:bg-gray-700 transition-colors",children:h.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]})}),h.jsxs("div",{className:"flex-1 overflow-y-auto",children:[h.jsxs("div",{className:"grid grid-cols-7 text-center p-4 gap-1",children:[["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(c=>h.jsx("div",{className:"py-2 text-gray-400 text-sm font-medium",children:c},c)),a.map((c,v)=>h.jsx("div",{onClick:()=>c.day&&E(c),className:`
                aspect-square relative flex flex-col p-2 rounded-lg cursor-pointer transition-colors
                ${c.isEmpty?"cursor-default":"hover:bg-gray-700"}
                ${c.isToday?"border-2 border-blue-500":""}
                ${c.isSelected?"bg-blue-900":"bg-gray-800"}
              `,children:c.day&&h.jsxs(h.Fragment,{children:[h.jsx("span",{className:`text-sm ${c.isToday?"font-bold text-white":"text-gray-400"}`,children:c.day}),c.hasEvents&&h.jsxs("div",{className:"mt-auto flex flex-wrap gap-1",children:[c.eventCounts.emergency>0&&h.jsx("span",{className:"w-3 h-3 bg-red-500 rounded-full animate-pulse"}),c.eventCounts.symptom>0&&h.jsx("span",{className:"w-3 h-3 bg-orange-500 rounded-full"}),c.eventCounts.medication>0&&h.jsx("span",{className:"w-3 h-3 bg-green-500 rounded-full"}),c.eventCounts.message>0&&h.jsx("span",{className:"w-3 h-3 bg-blue-500 rounded-full"})]})]})},v))]}),o&&h.jsxs("div",{className:"bg-gray-800 p-4 mt-4 mx-4 mb-4 rounded-lg",children:[h.jsx("h3",{className:"text-lg font-bold mb-4",children:o.toLocaleDateString(void 0,{month:"long",day:"numeric",year:"numeric"})}),f.length===0?h.jsxs("div",{className:"text-center py-8 text-gray-400",children:[h.jsx("svg",{className:"w-12 h-12 mx-auto text-gray-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),h.jsx("p",{className:"mt-2",children:"No events for this day"})]}):h.jsx("div",{className:"space-y-3",children:f.map((c,v)=>{var x,k,_,j,P,$;return h.jsx("div",{className:`p-3 rounded-lg cursor-pointer border border-gray-700 hover:bg-gray-700 transition-colors
                      ${((x=c.payload)==null?void 0:x.severity)==="critical"?"bg-red-900 border-red-700":((k=c.payload)==null?void 0:k.severity)==="high"?"bg-orange-900 border-orange-700":"bg-gray-800"}`,children:h.jsxs("div",{className:"flex justify-between items-start",children:[h.jsxs("div",{className:"flex space-x-3",children:[h.jsx("span",{className:"text-2xl",children:M(c)}),h.jsxs("div",{children:[h.jsx("p",{className:"text-white font-medium mb-1",children:c.subtype==="symptom"?`Reported symptom: ${((_=c.payload)==null?void 0:_.text)||"Unspecified"}`:c.subtype==="recipe"?`Medication: ${((j=c.payload)==null?void 0:j.medication)||"Unspecified"}`:((P=c.payload)==null?void 0:P.text)||"Message"}),h.jsxs("p",{className:"text-xs text-gray-400",children:[p(c.timestamp)," • ",c.from==="patient"?"From Patient":"From Doctor"]})]})]}),(($=c.payload)==null?void 0:$.severity)&&h.jsx("span",{className:`text-xs font-bold px-2 py-1 rounded ${c.payload.severity==="critical"?"bg-red-500 text-white":c.payload.severity==="high"?"bg-orange-500 text-white":"bg-blue-500 text-white"}`,children:c.payload.severity.toUpperCase()})]})},v)})})]})]})]})},_h=({patient:e,messages:t,onBack:n})=>{var E,N,M,p,a,f;const[r,i]=ne.useState([]),[s,o]=ne.useState([]);ne.useEffect(()=>{if(!t||t.length===0)return;const c=[],v=[],x=[...t].sort((k,_)=>new Date(k.timestamp)-new Date(_.timestamp));x.forEach((k,_)=>{if(k.subtype==="preview")return;const j=l(k),P=`node-${_}`;if(c.push({id:P,type:j,message:k,x:100+_*150%600,y:100+Math.floor(_*150/600)*120}),_>0){const $=`node-${_-1}`;v.push({source:$,target:P,type:"chronological"})}if(j==="medication"&&_>0){for(let $=_-1;$>=Math.max(0,_-5);$--)if(l(x[$])==="symptom"){v.push({source:`node-${$}`,target:P,type:"related"});break}}}),i(c),o(v)},[t]);const l=c=>{var v,x;return c.subtype==="symptom"?"symptom":c.subtype==="recipe"?"medication":((v=c.payload)==null?void 0:v.severity)==="critical"||((x=c.payload)==null?void 0:x.severity)==="high"?"emergency":"message"},u=c=>{switch(c){case"symptom":return"#f97316";case"medication":return"#22c55e";case"emergency":return"#ef4444";default:return"#3b82f6"}},d=c=>{switch(c){case"symptom":return"🩺";case"medication":return"💊";case"emergency":return"🚨";default:return"💬"}},g=c=>c==="related"?"stroke-dasharray: 5,5; stroke: #9333ea; stroke-width: 2;":"stroke: #475569; stroke-width: 1.5;",y=c=>new Date(c).toLocaleString(void 0,{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),[m,S]=ne.useState(null);return h.jsxs("div",{className:"h-full bg-gray-900 text-white p-6 overflow-hidden flex flex-col",children:[h.jsxs("div",{className:"flex justify-between items-center mb-6",children:[h.jsxs("div",{className:"flex items-center space-x-4",children:[h.jsx("button",{onClick:n,className:"p-2 rounded-full hover:bg-gray-700 transition-colors",children:h.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),h.jsxs("div",{children:[h.jsxs("h2",{className:"text-2xl font-bold",children:[e.name," - Journey Graph"]}),h.jsx("p",{className:"text-gray-400 text-sm",children:"Interactive timeline visualization"})]})]}),h.jsxs("div",{className:"flex space-x-4",children:[h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx("span",{className:"w-3 h-3 rounded-full bg-orange-500"}),h.jsx("span",{className:"text-sm text-gray-300",children:"Symptoms"})]}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx("span",{className:"w-3 h-3 rounded-full bg-green-500"}),h.jsx("span",{className:"text-sm text-gray-300",children:"Medications"})]}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx("span",{className:"w-3 h-3 rounded-full bg-red-500"}),h.jsx("span",{className:"text-sm text-gray-300",children:"Emergencies"})]}),h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx("span",{className:"w-3 h-3 rounded-full bg-blue-500"}),h.jsx("span",{className:"text-sm text-gray-300",children:"Messages"})]})]})]}),h.jsx("div",{className:"flex-1 border border-gray-800 rounded-lg overflow-hidden relative",children:r.length>0?h.jsxs("svg",{width:"100%",height:"100%",className:"bg-gray-900",children:[s.map((c,v)=>{const x=r.find(_=>_.id===c.source),k=r.find(_=>_.id===c.target);return!x||!k?null:h.jsxs("g",{children:[h.jsx("line",{x1:x.x+25,y1:x.y+25,x2:k.x+25,y2:k.y+25,style:g(c.type)}),c.type==="related"&&h.jsx("polygon",{points:`${k.x+25-6},${k.y+25-4} ${k.x+25},${k.y+25} ${k.x+25-6},${k.y+25+4}`,fill:"#9333ea",transform:`rotate(${Math.atan2(k.y-x.y,k.x-x.x)*180/Math.PI}, ${k.x+25}, ${k.y+25})`})]},`conn-${v}`)}),r.map(c=>h.jsxs("g",{onClick:()=>S((m==null?void 0:m.id)===c.id?null:c),className:"cursor-pointer",transform:`translate(${c.x}, ${c.y})`,children:[h.jsx("circle",{cx:"25",cy:"25",r:"25",fill:u(c.type),className:`${c.type==="emergency"?"animate-pulse":""}`}),h.jsx("text",{x:"25",y:"25",textAnchor:"middle",dominantBaseline:"central",fontSize:"18",children:d(c.type)}),h.jsx("text",{x:"25",y:"55",textAnchor:"middle",fill:"#9ca3af",fontSize:"8",children:y(c.message.timestamp).split(",")[0]})]},c.id))]}):h.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:h.jsxs("div",{className:"text-center",children:[h.jsx("svg",{className:"w-12 h-12 mx-auto text-gray-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),h.jsx("p",{className:"mt-3",children:"No patient data available"})]})})}),m&&h.jsxs("div",{className:"mt-4 bg-gray-800 p-4 rounded-lg border border-gray-700",children:[h.jsxs("div",{className:"flex justify-between items-start",children:[h.jsxs("div",{className:"flex space-x-3",children:[h.jsx("div",{className:"w-10 h-10 rounded-full flex items-center justify-center",style:{backgroundColor:u(m.type)},children:h.jsx("span",{className:"text-xl",children:d(m.type)})}),h.jsxs("div",{children:[h.jsxs("h3",{className:"text-lg font-bold text-white",children:[m.type==="symptom"&&"Symptom Report",m.type==="medication"&&"Medication Update",m.type==="emergency"&&"Emergency Alert",m.type==="message"&&"Communication"]}),h.jsx("p",{className:"text-sm text-gray-400",children:y(m.message.timestamp)})]})]}),h.jsx("button",{onClick:()=>S(null),className:"text-gray-400 hover:text-white",children:h.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),h.jsxs("div",{className:"mt-3 bg-gray-700 p-3 rounded border border-gray-600",children:[((E=m.message.payload)==null?void 0:E.text)&&h.jsx("p",{className:"text-white mb-3",children:m.message.payload.text}),((N=m.message.payload)==null?void 0:N.symptoms)&&h.jsxs("div",{className:"mt-2",children:[h.jsx("span",{className:"text-gray-300 text-sm font-medium",children:"Related Symptoms:"}),h.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:m.message.payload.symptoms.map((c,v)=>h.jsx("span",{className:"px-2 py-1 bg-orange-900 text-orange-100 text-xs rounded-full",children:c.name},v))})]}),((M=m.message.payload)==null?void 0:M.medication)&&h.jsxs("div",{className:"mt-2",children:[h.jsx("span",{className:"text-gray-300 text-sm font-medium",children:"Medication:"}),h.jsx("p",{className:"text-white",children:m.message.payload.medication}),((p=m.message.payload)==null?void 0:p.dosage)&&h.jsxs("p",{className:"text-sm text-gray-300",children:["Dosage: ",m.message.payload.dosage]}),((a=m.message.payload)==null?void 0:a.frequency)&&h.jsxs("p",{className:"text-sm text-gray-300",children:["Frequency: ",m.message.payload.frequency]})]}),((f=m.message.payload)==null?void 0:f.severity)&&h.jsx("div",{className:"mt-2",children:h.jsxs("span",{className:`px-2 py-1 rounded text-xs font-bold ${m.message.payload.severity==="critical"?"bg-red-500 text-white":m.message.payload.severity==="high"?"bg-orange-500 text-white":"bg-blue-500 text-white"}`,children:[m.message.payload.severity.toUpperCase()," PRIORITY"]})})]})]})]})},Tu="http://localhost:3000";function Eh(){const[e,t]=ne.useState([]),[n,r]=ne.useState(null),[i,s]=ne.useState("disconnected"),[o,l]=ne.useState("patients"),[u,d]=ne.useState(null),[g,y]=ne.useState([]),[m,S]=ne.useState("");ne.useEffect(()=>{const f=Xr(Tu);return r(f),f.on("connect",()=>{s("connected")}),f.on("disconnect",()=>{s("disconnected")}),f.on("newMessage",c=>{c.message&&t(v=>[...v,c.message])}),fetch(`${Tu}/messages`).then(c=>c.json()).then(c=>{t(c),E(c)}).catch(c=>console.error("Failed to load messages:",c)),()=>f.close()},[]);const E=f=>{const c=new Map;f.forEach(v=>{if(v.from==="patient"||v.patientId){const x=v.patientId||v.from,k=v.patientName||`Patient ${x}`;if(!c.has(x))c.set(x,{id:x,name:k,lastMessage:v.timestamp,messageCount:1,status:N(v),avatar:`https://api.dicebear.com/7.x/avataaars/svg?seed=${x}`});else{const _=c.get(x);_.messageCount++,new Date(v.timestamp)>new Date(_.lastMessage)&&(_.lastMessage=v.timestamp,_.status=N(v))}}}),y(Array.from(c.values()))},N=f=>f.subtype==="emergency"?"emergency":f.subtype==="symptoms"?"symptoms":f.subtype==="medication"?"stable":"normal",M=g.filter(f=>f.name.toLowerCase().includes(m.toLowerCase())),p=f=>{d(f),l("calendar")},a=()=>{switch(o){case"patients":return h.jsxs("div",{className:"p-8",children:[h.jsx("div",{className:"mb-8",children:h.jsxs("div",{className:"relative max-w-md",children:[h.jsx("input",{type:"text",placeholder:"Search patients...",value:m,onChange:f=>S(f.target.value),className:"w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 pl-12 text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),h.jsx("svg",{className:"absolute left-4 top-3.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:h.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]})}),h.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:M.map(f=>h.jsx(kh,{patient:f,onClick:()=>p(f)},f.id))})]});case"calendar":return h.jsx(Sh,{patient:u,messages:e.filter(f=>f.patientId===(u==null?void 0:u.id)||f.from===(u==null?void 0:u.id)),onBack:()=>l("patients"),onViewGraph:()=>l("graph")});case"graph":return h.jsx(_h,{patient:u,messages:e.filter(f=>f.patientId===(u==null?void 0:u.id)||f.from===(u==null?void 0:u.id)),onBack:()=>l("calendar")});default:return null}};return h.jsxs("div",{className:"min-h-screen bg-gray-900 text-white",children:[h.jsx("header",{className:"bg-gray-800 border-b border-gray-700 p-6",children:h.jsxs("div",{className:"flex justify-between items-center",children:[h.jsxs("div",{className:"flex items-center space-x-4",children:[h.jsx("div",{className:"w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center",children:h.jsx("span",{className:"text-2xl",children:"🏥"})}),h.jsxs("div",{children:[h.jsx("h1",{className:"text-2xl font-bold text-white",children:"Symptom-OS"}),h.jsx("p",{className:"text-gray-400 text-sm",children:"Doctor Dashboard"})]})]}),h.jsxs("div",{className:"flex items-center space-x-6",children:[u&&o!=="patients"&&h.jsx("div",{className:"text-lg font-semibold text-white",children:u.name}),h.jsx("div",{className:`px-4 py-2 rounded-full text-sm font-semibold ${i==="connected"?"bg-green-900 text-green-300 border border-green-700":"bg-red-900 text-red-300 border border-red-700"}`,children:h.jsxs("div",{className:"flex items-center space-x-2",children:[h.jsx("div",{className:`w-2 h-2 rounded-full ${i==="connected"?"bg-green-400":"bg-red-400"} animate-pulse`}),h.jsx("span",{className:"capitalize",children:i})]})})]})]})}),u&&o!=="patients"&&h.jsx("nav",{className:"bg-gray-800 border-b border-gray-700 px-6 py-3",children:h.jsxs("div",{className:"flex space-x-6",children:[h.jsx("button",{onClick:()=>l("calendar"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${o==="calendar"?"bg-blue-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,children:"Calendar"}),h.jsx("button",{onClick:()=>l("graph"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${o==="graph"?"bg-blue-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,children:"Graph"})]})}),h.jsx("main",{className:"flex-1",children:a()})]})}hs.createRoot(document.getElementById("root")).render(h.jsx(yf.StrictMode,{children:h.jsx(Eh,{})}));
