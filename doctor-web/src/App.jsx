import React, { useState, useEffect } from 'react';
import io from 'socket.io-client';
import PatientCard from './components/PatientCard';
import CalendarView from './components/CalendarView';
import GraphView from './components/GraphView';

const SERVER_URL = 'http://localhost:3000';

function App() {
  const [messages, setMessages] = useState([]);
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [currentView, setCurrentView] = useState('patients'); // 'patients', 'calendar', 'graph'
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [patients, setPatients] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      setConnectionStatus('connected');
    });

    newSocket.on('disconnect', () => {
      setConnectionStatus('disconnected');
    });

    newSocket.on('newMessage', (data) => {
      if (data.message) {
        setMessages(prev => [...prev, data.message]);
      }
    });

    // Listen for bridge messages requiring approval
    newSocket.on('bridgeMessage', (data) => {
      console.log('Bridge message received:', data);
      if (data.requiresApproval) {
        // Add bridge message to messages for doctor review
        setMessages(prev => [...prev, data.message]);

        // Show notification for approval needed
        setTimeout(() => {
          const approvalNotification = {
            id: `approval-${Date.now()}`,
            from: 'system',
            subtype: 'approval_needed',
            payload: {
              text: `⚠️ Bridge message requires approval: "${data.message.payload.translatedMessage}"`,
              originalBridgeId: data.message.id
            },
            timestamp: new Date().toISOString()
          };
          setMessages(prev => [...prev, approvalNotification]);
        }, 500);
      }
    });

    // Listen for clinical summaries from patient messages
    newSocket.on('clinicalSummary', (data) => {
      console.log('Clinical summary received:', data);
      const summaryMessage = {
        id: `summary-${Date.now()}`,
        from: 'ai_bridge',
        subtype: 'clinical_summary',
        payload: {
          text: `📋 Patient Analysis: ${data.message.payload.clinicalSummary}`,
          urgency: data.urgency,
          patientId: data.patientId
        },
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, summaryMessage]);
    });

    // Load initial data
    fetch(`${SERVER_URL}/messages`)
      .then(res => res.json())
      .then(data => {
        setMessages(data);
        extractPatients(data);
      })
      .catch(err => console.error('Failed to load messages:', err));

    return () => newSocket.close();
  }, []);

  const extractPatients = (messages) => {
    const patientMap = new Map();
    
    messages.forEach(msg => {
      if (msg.from === 'patient' || msg.patientId) {
        const patientId = msg.patientId || msg.from;
        const patientName = msg.patientName || `Patient ${patientId}`;
        
        if (!patientMap.has(patientId)) {
          patientMap.set(patientId, {
            id: patientId,
            name: patientName,
            lastMessage: msg.timestamp,
            messageCount: 1,
            status: getPatientStatus(msg),
            avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${patientId}`
          });
        } else {
          const patient = patientMap.get(patientId);
          patient.messageCount++;
          if (new Date(msg.timestamp) > new Date(patient.lastMessage)) {
            patient.lastMessage = msg.timestamp;
            patient.status = getPatientStatus(msg);
          }
        }
      }
    });

    setPatients(Array.from(patientMap.values()));
  };

  const getPatientStatus = (message) => {
    if (message.subtype === 'emergency') return 'emergency';
    if (message.subtype === 'symptoms') return 'symptoms';
    if (message.subtype === 'medication') return 'stable';
    return 'normal';
  };

  const filteredPatients = patients.filter(patient =>
    patient.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
    setCurrentView('calendar');
  };

  const renderMainContent = () => {
    switch (currentView) {
      case 'patients':
        return (
          <div className="p-8">
            <div className="mb-8">
              <div className="relative max-w-md">
                <input
                  type="text"
                  placeholder="Search patients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-700 rounded-xl px-4 py-3 pl-12 text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <svg className="absolute left-4 top-3.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredPatients.map(patient => (
                <PatientCard
                  key={patient.id}
                  patient={patient}
                  onClick={() => handlePatientSelect(patient)}
                />
              ))}
            </div>
          </div>
        );
      
      case 'calendar':
        return (
          <CalendarView
            patient={selectedPatient}
            messages={messages.filter(m => m.patientId === selectedPatient?.id || m.from === selectedPatient?.id)}
            onBack={() => setCurrentView('patients')}
            onViewGraph={() => setCurrentView('graph')}
          />
        );
      
      case 'graph':
        return (
          <GraphView
            patient={selectedPatient}
            messages={messages.filter(m => m.patientId === selectedPatient?.id || m.from === selectedPatient?.id)}
            onBack={() => setCurrentView('calendar')}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
              <span className="text-2xl">🏥</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">Lyxara</h1>
              <p className="text-gray-400 text-sm">AI Medical Intelligence</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-6">
            {selectedPatient && currentView !== 'patients' && (
              <div className="text-lg font-semibold text-white">
                {selectedPatient.name}
              </div>
            )}
            
            <div className={`px-4 py-2 rounded-full text-sm font-semibold ${
              connectionStatus === 'connected' 
                ? 'bg-green-900 text-green-300 border border-green-700' 
                : 'bg-red-900 text-red-300 border border-red-700'
            }`}>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  connectionStatus === 'connected' ? 'bg-green-400' : 'bg-red-400'
                } animate-pulse`}></div>
                <span className="capitalize">{connectionStatus}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      {selectedPatient && currentView !== 'patients' && (
        <nav className="bg-gray-800 border-b border-gray-700 px-6 py-3">
          <div className="flex space-x-6">
            <button
              onClick={() => setCurrentView('calendar')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                currentView === 'calendar' 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              Calendar
            </button>
            <button
              onClick={() => setCurrentView('graph')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                currentView === 'graph' 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              Graph
            </button>
          </div>
        </nav>
      )}

      {/* Main Content */}
      <main className="flex-1">
        {renderMainContent()}
      </main>
    </div>
  );
}

export default App;