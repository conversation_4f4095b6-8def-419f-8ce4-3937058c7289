import React, { useState, useEffect } from 'react';

const GraphView = ({ patient, messages, onBack }) => {
  const [nodes, setNodes] = useState([]);
  const [connections, setConnections] = useState([]);
  
  // Process messages to create graph nodes and connections
  useEffect(() => {
    if (!messages || messages.length === 0) return;
    
    const timelineNodes = [];
    const timelineConnections = [];
    
    // Sort messages by timestamp
    const sortedMessages = [...messages].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    
    // Create nodes for each message
    sortedMessages.forEach((message, index) => {
      // Skip preview messages
      if (message.subtype === 'preview') return;
      
      // Create node for the message
      const nodeType = getNodeType(message);
      const nodeId = `node-${index}`;
      
      timelineNodes.push({
        id: nodeId,
        type: nodeType,
        message,
        x: 100 + (index * 180) % 1000, // Better spacing across canvas
        y: 100 + Math.floor((index * 180) / 1000) * 150 // More vertical space
      });
      
      // Create connections between nodes (chronological order)
      if (index > 0) {
        const prevNodeId = `node-${index - 1}`;
        timelineConnections.push({
          source: prevNodeId,
          target: nodeId,
          type: 'chronological'
        });
      }
      
      // Create connections between related nodes (e.g. symptom -> medication)
      if (nodeType === 'medication' && index > 0) {
        // Find recent symptom nodes
        for (let i = index - 1; i >= Math.max(0, index - 5); i--) {
          if (getNodeType(sortedMessages[i]) === 'symptom') {
            timelineConnections.push({
              source: `node-${i}`,
              target: nodeId,
              type: 'related'
            });
            break;
          }
        }
      }
    });
    
    setNodes(timelineNodes);
    setConnections(timelineConnections);
  }, [messages]);
  
  // Get node type from message
  const getNodeType = (message) => {
    if (message.subtype === 'symptom') return 'symptom';
    if (message.subtype === 'recipe') return 'medication';
    if (message.payload?.severity === 'critical' || message.payload?.severity === 'high') return 'emergency';
    return 'message';
  };
  
  // Get node color based on type
  const getNodeColor = (type) => {
    switch (type) {
      case 'symptom': return '#f97316'; // orange-500
      case 'medication': return '#22c55e'; // green-500
      case 'emergency': return '#ef4444'; // red-500
      default: return '#3b82f6'; // blue-500
    }
  };
  
  // Get node icon based on type
  const getNodeIcon = (type) => {
    switch (type) {
      case 'symptom': return '🩺';
      case 'medication': return '💊';
      case 'emergency': return '🚨';
      default: return '💬';
    }
  };
  
  // Get connection style based on type
  const getConnectionStyle = (type) => {
    return type === 'related' 
      ? { strokeDasharray: '5,5', stroke: '#9333ea', strokeWidth: 2 } // purple for related
      : { stroke: '#475569', strokeWidth: 1.5 }; // slate for chronological
  };
  
  // Format timestamp
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleString(undefined, {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Node detail display
  const [selectedNode, setSelectedNode] = useState(null);
  
  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      <div className="flex justify-between items-center p-6 border-b border-gray-700 flex-shrink-0">
        <div className="flex items-center space-x-4">
          <button 
            onClick={onBack}
            className="p-2 rounded-full hover:bg-gray-700 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h2 className="text-2xl font-bold">{patient.name} - Journey Graph</h2>
            <p className="text-gray-400 text-sm">Interactive timeline visualization</p>
          </div>
        </div>
        <div className="flex space-x-4">
          <div className="flex items-center space-x-2">
            <span className="w-3 h-3 rounded-full bg-orange-500"></span>
            <span className="text-sm text-gray-300">Symptoms</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-3 h-3 rounded-full bg-green-500"></span>
            <span className="text-sm text-gray-300">Medications</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-3 h-3 rounded-full bg-red-500"></span>
            <span className="text-sm text-gray-300">Emergencies</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-3 h-3 rounded-full bg-blue-500"></span>
            <span className="text-sm text-gray-300">Messages</span>
          </div>
        </div>
      </div>
      
      <div className="flex-1 relative overflow-hidden">
        {/* Canvas-like SVG Graph */}
        {nodes.length > 0 ? (
          <svg 
            width="100%" 
            height="100%" 
            className="bg-gray-900"
            viewBox="0 0 1200 800"
            preserveAspectRatio="xMidYMid meet"
            style={{ minHeight: '600px' }}
          >
            {/* Grid Background */}
            <defs>
              <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#374151" strokeWidth="1" opacity="0.3"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
            
            {/* Connections */}
            {connections.map((conn, i) => {
              const source = nodes.find(n => n.id === conn.source);
              const target = nodes.find(n => n.id === conn.target);
              
              if (!source || !target) return null;
              
              return (
                <g key={`conn-${i}`}>
                  <line
                    x1={source.x + 25}
                    y1={source.y + 25}
                    x2={target.x + 25}
                    y2={target.y + 25}
                    style={getConnectionStyle(conn.type)}
                  />
                  {conn.type === 'related' && (
                    <polygon 
                      points={`${target.x + 25 - 6},${target.y + 25 - 4} ${target.x + 25},${target.y + 25} ${target.x + 25 - 6},${target.y + 25 + 4}`}
                      fill="#9333ea"
                      transform={`rotate(${Math.atan2(target.y - source.y, target.x - source.x) * 180 / Math.PI}, ${target.x + 25}, ${target.y + 25})`}
                    />
                  )}
                </g>
              );
            })}
            
            {/* Nodes */}
            {nodes.map((node) => (
              <g 
                key={node.id}
                onClick={() => setSelectedNode(selectedNode?.id === node.id ? null : node)}
                className="cursor-pointer"
                transform={`translate(${node.x}, ${node.y})`}
              >
                <circle
                  cx="25"
                  cy="25"
                  r="25"
                  fill={getNodeColor(node.type)}
                  className={`${node.type === 'emergency' ? 'animate-pulse' : ''}`}
                />
                <text
                  x="25"
                  y="25"
                  textAnchor="middle"
                  dominantBaseline="central"
                  fontSize="18"
                >
                  {getNodeIcon(node.type)}
                </text>
                {/* Small timestamp below node */}
                <text
                  x="25"
                  y="55"
                  textAnchor="middle"
                  fill="#9ca3af"
                  fontSize="8"
                >
                  {formatTime(node.message.timestamp).split(',')[0]}
                </text>
              </g>
            ))}
          </svg>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <svg className="w-12 h-12 mx-auto text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p className="mt-3">No patient data available</p>
            </div>
          </div>
        )}
      </div>
      
      {/* Selected Node Details Panel - Fixed Position */}
      {selectedNode && (
        <div className="absolute top-4 right-4 w-80 bg-gray-800 p-4 rounded-lg border border-gray-700 shadow-2xl z-10">
          <div className="flex justify-between items-start">
            <div className="flex space-x-3">
              <div className="w-10 h-10 rounded-full flex items-center justify-center" style={{ backgroundColor: getNodeColor(selectedNode.type) }}>
                <span className="text-xl">{getNodeIcon(selectedNode.type)}</span>
              </div>
              <div>
                <h3 className="text-lg font-bold text-white">
                  {selectedNode.type === 'symptom' && 'Symptom Report'}
                  {selectedNode.type === 'medication' && 'Medication Update'}
                  {selectedNode.type === 'emergency' && 'Emergency Alert'}
                  {selectedNode.type === 'message' && 'Communication'}
                </h3>
                <p className="text-sm text-gray-400">{formatTime(selectedNode.message.timestamp)}</p>
              </div>
            </div>
            <button 
              onClick={() => setSelectedNode(null)} 
              className="text-gray-400 hover:text-white"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="mt-3 bg-gray-700 p-3 rounded border border-gray-600 max-h-60 overflow-y-auto">
            {selectedNode.message.payload?.text && (
              <p className="text-white mb-3">{selectedNode.message.payload.text}</p>
            )}
            
            {selectedNode.message.payload?.symptoms && (
              <div className="mt-2">
                <span className="text-gray-300 text-sm font-medium">Related Symptoms:</span>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedNode.message.payload.symptoms.map((symptom, i) => (
                    <span key={i} className="px-2 py-1 bg-orange-900 text-orange-100 text-xs rounded-full">
                      {symptom.name}
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            {selectedNode.message.payload?.medication && (
              <div className="mt-2">
                <span className="text-gray-300 text-sm font-medium">Medication:</span>
                <p className="text-white">{selectedNode.message.payload.medication}</p>
                {selectedNode.message.payload?.dosage && (
                  <p className="text-sm text-gray-300">Dosage: {selectedNode.message.payload.dosage}</p>
                )}
                {selectedNode.message.payload?.frequency && (
                  <p className="text-sm text-gray-300">Frequency: {selectedNode.message.payload.frequency}</p>
                )}
              </div>
            )}
            
            {selectedNode.message.payload?.severity && (
              <div className="mt-2">
                <span className={`px-2 py-1 rounded text-xs font-bold ${
                  selectedNode.message.payload.severity === 'critical' ? 'bg-red-500 text-white' :
                  selectedNode.message.payload.severity === 'high' ? 'bg-orange-500 text-white' :
                  'bg-blue-500 text-white'
                }`}>
                  {selectedNode.message.payload.severity.toUpperCase()} PRIORITY
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default GraphView;
