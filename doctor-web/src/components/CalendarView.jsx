import React, { useState } from 'react';

const CalendarView = ({ patient, messages, onBack, onViewGraph }) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);
  
  // Get days in month
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };
  
  // Get day of week for first day of month (0 = Sunday, 6 = Saturday)
  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };
  
  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);
    
    const days = [];
    
    // Add empty slots for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push({ day: null, isEmpty: true });
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateKey = date.toISOString().split('T')[0];
      
      // Check if there are events on this day
      const hasEvents = messages.some(message => {
        const messageDate = new Date(message.timestamp);
        return messageDate.getFullYear() === year &&
               messageDate.getMonth() === month &&
               messageDate.getDate() === day;
      });
      
      const eventsForDay = messages.filter(message => {
        const messageDate = new Date(message.timestamp);
        return messageDate.getFullYear() === year &&
               messageDate.getMonth() === month &&
               messageDate.getDate() === day;
      });
      
      // Count events by type
      const eventCounts = {
        symptom: 0,
        medication: 0,
        emergency: 0,
        message: 0
      };
      
      eventsForDay.forEach(message => {
        if (message.subtype === 'symptom') eventCounts.symptom++;
        else if (message.subtype === 'recipe') eventCounts.medication++;
        else if (message.payload?.severity === 'critical' || message.payload?.severity === 'high') eventCounts.emergency++;
        else eventCounts.message++;
      });
      
      days.push({
        day,
        date,
        dateKey,
        hasEvents,
        eventCounts,
        isToday: new Date().toDateString() === date.toDateString(),
        isSelected: selectedDate && selectedDate.toDateString() === date.toDateString()
      });
    }
    
    return days;
  };
  
  // Format month name
  const formatMonthName = () => {
    const options = { month: 'long', year: 'numeric' };
    return currentMonth.toLocaleDateString(undefined, options);
  };
  
  // Navigate to previous month
  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };
  
  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };
  
  // Handle date click
  const handleDateClick = (day) => {
    if (!day.day) return;
    setSelectedDate(day.date);
    
    // Get events for selected day
    const eventsForDay = messages.filter(message => {
      const messageDate = new Date(message.timestamp);
      return messageDate.toDateString() === day.date.toDateString();
    });
    
    // Sort events by time
    eventsForDay.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  };
  
  // Get events for selected date
  const getEventsForSelectedDate = () => {
    if (!selectedDate) return [];
    
    return messages.filter(message => {
      const messageDate = new Date(message.timestamp);
      return messageDate.toDateString() === selectedDate.toDateString();
    }).sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  };

  // Get icon for message type
  const getEventIcon = (message) => {
    if (message.payload?.severity === 'critical' || message.payload?.severity === 'high') return '🚨';
    switch (message.subtype) {
      case 'symptom': return '🩺';
      case 'recipe': return '💊';
      case 'image': return '📷';
      default: return '💬';
    }
  };

  // Format event time
  const formatEventTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const days = generateCalendarDays();
  const selectedEvents = getEventsForSelectedDate();

  return (
    <div className="flex flex-col h-full bg-gray-900 text-white">
      {/* Calendar Header */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <button 
              onClick={onBack}
              className="p-2 rounded-full hover:bg-gray-700 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div>
              <h2 className="text-2xl font-bold text-white">{patient.name} - {formatMonthName()}</h2>
              <p className="text-gray-400 text-sm">Patient interactions timeline</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button 
              onClick={onViewGraph}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
            >
              📊 Graph View
            </button>
            <button 
              onClick={prevMonth}
              className="p-2 rounded-full hover:bg-gray-700 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button 
              onClick={nextMonth}
              className="p-2 rounded-full hover:bg-gray-700 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="flex-1 overflow-y-auto">
        <div className="grid grid-cols-7 text-center p-4 gap-1">
          {/* Day headers */}
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="py-2 text-gray-400 text-sm font-medium">
              {day}
            </div>
          ))}
          
          {/* Calendar days */}
          {days.map((day, i) => (
            <div 
              key={i}
              onClick={() => day.day && handleDateClick(day)}
              className={`
                aspect-square relative flex flex-col p-2 rounded-lg cursor-pointer transition-colors
                ${day.isEmpty ? 'cursor-default' : 'hover:bg-gray-700'}
                ${day.isToday ? 'border-2 border-blue-500' : ''}
                ${day.isSelected ? 'bg-blue-900' : 'bg-gray-800'}
              `}
            >
              {day.day && (
                <>
                  <span className={`text-sm ${day.isToday ? 'font-bold text-white' : 'text-gray-400'}`}>
                    {day.day}
                  </span>
                  
                  {day.hasEvents && (
                    <div className="mt-auto flex flex-wrap gap-1">
                      {day.eventCounts.emergency > 0 && (
                        <span className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
                      )}
                      {day.eventCounts.symptom > 0 && (
                        <span className="w-3 h-3 bg-orange-500 rounded-full"></span>
                      )}
                      {day.eventCounts.medication > 0 && (
                        <span className="w-3 h-3 bg-green-500 rounded-full"></span>
                      )}
                      {day.eventCounts.message > 0 && (
                        <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </div>

        {/* Events for selected date */}
        {selectedDate && (
          <div className="bg-gray-800 p-4 mt-4 mx-4 mb-4 rounded-lg">
            <h3 className="text-lg font-bold mb-4">
              {selectedDate.toLocaleDateString(undefined, { month: 'long', day: 'numeric', year: 'numeric' })}
            </h3>
            
            {selectedEvents.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                <svg className="w-12 h-12 mx-auto text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="mt-2">No events for this day</p>
              </div>
            ) : (
              <div className="space-y-3">
                {selectedEvents.map((event, i) => (
                  <div 
                    key={i}
                    className={`p-3 rounded-lg cursor-pointer border border-gray-700 hover:bg-gray-700 transition-colors
                      ${event.payload?.severity === 'critical' ? 'bg-red-900 border-red-700' : 
                        event.payload?.severity === 'high' ? 'bg-orange-900 border-orange-700' : 'bg-gray-800'}`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex space-x-3">
                        <span className="text-2xl">{getEventIcon(event)}</span>
                        <div>
                          <p className="text-white font-medium mb-1">
                            {event.subtype === 'symptom'
                              ? `Reported symptom: ${event.payload?.text || 'Unspecified'}`
                              : event.subtype === 'recipe'
                                ? `Medication: ${event.payload?.medication || 'Unspecified'}`
                                : event.payload?.text || 'Message'}
                          </p>
                          <p className="text-xs text-gray-400">
                            {formatEventTime(event.timestamp)} • {event.from === 'patient' ? 'From Patient' : 'From Doctor'}
                          </p>
                        </div>
                      </div>
                      
                      {event.payload?.severity && (
                        <span className={`text-xs font-bold px-2 py-1 rounded ${
                          event.payload.severity === 'critical' ? 'bg-red-500 text-white' :
                          event.payload.severity === 'high' ? 'bg-orange-500 text-white' :
                          'bg-blue-500 text-white'
                        }`}>
                          {event.payload.severity.toUpperCase()}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CalendarView;
