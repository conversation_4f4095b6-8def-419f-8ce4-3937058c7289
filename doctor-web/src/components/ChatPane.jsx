import React, { useState, useRef, useEffect } from 'react';

const ChatPane = ({ messages, onSendMessage, onCollapse, isCollapsed = false }) => {
  const [inputText, setInputText] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const [showResearch, setShowResearch] = useState(false);
  const [researchQuery, setResearchQuery] = useState('');
  const [researchResult, setResearchResult] = useState(null);
  const [isResearching, setIsResearching] = useState(false);
  const messagesEndRef = useRef(null);

  // Filter messages based on search
  const filteredMessages = messages.filter(message => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    const messageText = getMessageText(message).toLowerCase();
    return messageText.includes(searchLower);
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [filteredMessages]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!inputText.trim()) return;
    
    // Store the original message to show in UI immediately
    const originalMessage = inputText.trim();
    setInputText('');
    
    try {
      // Use the bridge API to translate doctor's message to patient-friendly language
      const response = await fetch('http://localhost:3000/bridge/doctor-to-patient', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: originalMessage,
          patientId: 'default-patient'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Bridge response:', result);
        
        // Display the original message in the UI (the bridge message will be shown to the patient)
        onSendMessage(originalMessage);
        
        // Show translation preview
        setTimeout(() => {
          // Show the translation preview as a system message
          const translationPreview = `Translation for patient: "${result.translatedMessage}"`;
          onSendMessage(translationPreview, 'system_preview');
        }, 500);
      } else {
        throw new Error('Bridge translation failed');
      }
    } catch (error) {
      console.error('Bridge error:', error);
      // Still send the original message if bridge fails
      onSendMessage(originalMessage);
    }
  };

  const handleResearch = async (e) => {
    e.preventDefault();
    if (!researchQuery.trim()) return;

    setIsResearching(true);
    try {
      const response = await fetch('http://localhost:3000/research/doctor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: researchQuery.trim(),
          patientId: 'default-patient'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setResearchResult(result);
      } else {
        throw new Error('Research failed');
      }
    } catch (error) {
      console.error('Research error:', error);
      setResearchResult({
        error: 'Failed to process research query. Please try again.'
      });
    } finally {
      setIsResearching(false);
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getMessageText = (message) => {
    switch (message.subtype) {
      case 'symptom':
        return `Symptom: "${message.payload.text}"`;
      case 'image':
        return `Shared image: ${message.payload.caption}`;
      case 'text':
        return message.payload.text;
      case 'recipe':
        return `Prescribed: ${message.payload.medication} ${message.payload.dosage}`;
      default:
        return 'Unknown message type';
    }
  };

  if (isCollapsed) {
    return (
      <div className="w-12 bg-white border-l border-gray-200 flex flex-col items-center py-4">
        <button
          onClick={onCollapse}
          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="Expand Chat"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 20l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
          </svg>
        </button>
        <div className="mt-2 text-xs text-gray-500 transform rotate-90 origin-center">
          {messages.length} msgs
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white/0">
      {/* Redesigned Header with Glass Effect */}
      <div className="p-6 border-b border-white/20 bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white text-lg">💬</span>
            </div>
            <div>
              <h3 className="font-bold text-gray-800 text-lg">Patient Communication</h3>
              <div className="flex items-center space-x-2">
                <span className="px-3 py-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs rounded-full font-semibold shadow-md">
                  {messages.length} messages
                </span>
                <span className="px-3 py-1 bg-green-100 text-green-700 text-xs rounded-full font-semibold">
                  🟢 Active
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="p-2 text-gray-500 hover:text-white hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-500 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg"
              title={isMinimized ? "Expand" : "Minimize"}
            >
              <svg className={`w-4 h-4 transition-transform ${isMinimized ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <button
              onClick={onCollapse}
              className="p-2 text-gray-500 hover:text-white hover:bg-gradient-to-r hover:from-red-500 hover:to-pink-500 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg"
              title="Collapse Chat"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
        
        {/* Enhanced Search */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search conversation..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-12 pr-4 py-3 border border-white/30 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/60 backdrop-blur-sm shadow-md placeholder-gray-500 font-medium"
          />
        </div>
      </div>

      {/* Messages */}
      {!isMinimized && (
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {filteredMessages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.from === 'doctor' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg text-sm ${
                  message.from === 'doctor'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-800'
                }`}
              >
                <p>{getMessageText(message)}</p>
                <p className={`text-xs mt-1 ${
                  message.from === 'doctor' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {formatTime(message.timestamp)}
                </p>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      )}

      {/* AI Research Panel */}
      {!isMinimized && showResearch && (
        <div className="border-t bg-purple-50 p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold text-purple-800 flex items-center">
              🔬 AI Medical Research Assistant
            </h4>
            <button
              onClick={() => setShowResearch(false)}
              className="text-purple-600 hover:text-purple-800"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleResearch} className="space-y-3">
            <div className="flex space-x-2">
              <input
                type="text"
                value={researchQuery}
                onChange={(e) => setResearchQuery(e.target.value)}
                placeholder="Ask about differential diagnoses, treatments, drug interactions..."
                className="flex-1 px-3 py-2 border border-purple-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <button
                type="submit"
                disabled={!researchQuery.trim() || isResearching}
                className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isResearching ? '🔄' : '🔍'}
              </button>
            </div>
          </form>

          {researchResult && (
            <div className="mt-4 p-3 bg-white rounded-lg border border-purple-200 max-h-64 overflow-y-auto">
              {researchResult.error ? (
                <p className="text-red-600">{researchResult.error}</p>
              ) : (
                <div>
                  <p className="text-sm font-medium text-purple-700 mb-2">
                    Query: {researchResult.query}
                  </p>
                  <div className="text-sm text-gray-800 whitespace-pre-wrap">
                    {researchResult.response}
                  </div>
                  {researchResult.metadata && (
                    <div className="mt-2 text-xs text-gray-500">
                      Model: {researchResult.metadata.model} |
                      Generated: {new Date(researchResult.metadata.timestamp).toLocaleTimeString()}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Input Section */}
      {!isMinimized && (
        <div className="border-t bg-gray-50 p-4">
          {/* Quick Actions Bar */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex space-x-2">
              <button className="px-3 py-1 bg-green-100 text-green-700 text-xs rounded-full hover:bg-green-200 transition-colors">
                📋 Templates
              </button>
              <button
                onClick={() => setShowResearch(!showResearch)}
                className={`px-3 py-1 text-xs rounded-full transition-colors ${
                  showResearch
                    ? 'bg-purple-500 text-white'
                    : 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                }`}
              >
                🔬 AI Research
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="flex space-x-2">
              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Type your response to the patient..."
                rows={2}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  }
                }}
              />
              <button
                type="submit"
                disabled={!inputText.trim()}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                <span>Send</span>
              </button>
            </div>
            
            {/* Quick Responses */}
            <div className="flex flex-wrap gap-1">
              {[
                '💊 Take with food and monitor symptoms',
                '📊 Please track your symptoms daily',
                '⏰ Schedule follow-up in 1 week',
                '🩺 Continue current treatment plan'
              ].map((quickResponse) => (
                <button
                  key={quickResponse}
                  onClick={() => setInputText(quickResponse)}
                  className="px-2 py-1 text-xs bg-white text-gray-600 rounded border hover:bg-gray-50 transition-colors"
                >
                  {quickResponse}
                </button>
              ))}
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default ChatPane;
