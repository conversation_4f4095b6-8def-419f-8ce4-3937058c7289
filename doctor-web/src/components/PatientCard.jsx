import React from 'react';

const PatientCard = ({ patient, onClick }) => {
  const getStatusColor = () => {
    switch (patient.status) {
      case 'emergency': return 'border-red-500 bg-gradient-to-br from-gray-800 to-red-900/50';
      case 'symptoms': return 'border-yellow-500 bg-gradient-to-br from-gray-800 to-yellow-900/50';
      case 'stable': return 'border-green-500 bg-gradient-to-br from-gray-800 to-green-900/50';
      default: return 'border-blue-500 bg-gradient-to-br from-gray-800 to-blue-900/50';
    }
  };

  const getLastActivityText = () => {
    if (!patient.lastMessage) return 'No activity';
    
    const timeSince = new Date() - new Date(patient.lastMessage);
    const hoursSince = Math.floor(timeSince / (1000 * 60 * 60));
    
    if (hoursSince < 1) return 'Just now';
    if (hoursSince < 24) return `${hoursSince}h ago`;
    return `${Math.floor(hoursSince / 24)}d ago`;
  };

  const getStatusIcon = () => {
    switch (patient.status) {
      case 'emergency': return '🚨';
      case 'symptoms': return '🔍';
      case 'stable': return '💊';
      default: return '👤';
    }
  };

  return (
    <div 
      onClick={() => onClick(patient)}
      className={`${getStatusColor()} cursor-pointer rounded-2xl border-2 shadow-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl overflow-hidden backdrop-blur-sm`}
    >
      <div className="p-6 h-full flex flex-col min-h-[280px]">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <img 
                src={patient.avatar} 
                alt={patient.name}
                className="w-16 h-16 rounded-full border-2 border-gray-600"
              />
              <span className="absolute -bottom-1 -right-1 text-2xl">
                {getStatusIcon()}
              </span>
            </div>
            <div>
              <h3 className="text-xl font-bold text-white mb-1">{patient.name}</h3>
              <p className="text-sm text-gray-300">ID: {patient.id}</p>
            </div>
          </div>
          {patient.status === 'emergency' && (
            <span className="bg-red-600 text-white px-3 py-1 rounded-full text-xs font-semibold animate-pulse">
              URGENT
            </span>
          )}
        </div>

        <div className="flex-1 space-y-3">
          <div className="bg-gray-800/50 rounded-lg p-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-400">Messages</span>
              <span className="text-white font-semibold">{patient.messageCount}</span>
            </div>
          </div>
          
          <div className="bg-gray-800/50 rounded-lg p-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-400">Status</span>
              <span className={`font-semibold capitalize ${
                patient.status === 'emergency' ? 'text-red-400' :
                patient.status === 'symptoms' ? 'text-yellow-400' :
                patient.status === 'stable' ? 'text-green-400' :
                'text-blue-400'
              }`}>
                {patient.status}
              </span>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center mt-4 text-xs text-gray-400 border-t border-gray-700 pt-3">
          <span>Last: {getLastActivityText()}</span>
          <span className="flex items-center space-x-1">
            <span>👁️</span>
            <span>View</span>
          </span>
        </div>
      </div>
    </div>
  );
};

export default PatientCard;
