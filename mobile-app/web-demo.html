<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lyxara - AI Health Companion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            background: #111827; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }
        .glass-card {
            background: rgba(31, 41, 55, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid #374151;
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <div class="glass-card rounded-2xl mx-5 mt-16 mb-10 p-6 shadow-2xl">
        <div class="flex items-center justify-center mb-2">
            <span class="text-3xl mr-3">🧠</span>
            <h1 class="text-3xl font-bold text-blue-400">Lyxara</h1>
        </div>
        <p class="text-center text-gray-400">Your AI Health Companion</p>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col items-center justify-center px-6">
        <!-- AI Visualizer -->
        <div class="mb-8">
            <div class="w-48 h-48 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center pulse-animation">
                <div class="w-40 h-40 rounded-full bg-gray-800 flex items-center justify-center">
                    <span class="text-6xl">🎙️</span>
                </div>
            </div>
        </div>

        <!-- Status Text -->
        <p class="text-center text-gray-300 mb-8 text-lg">
            Tap to share how you're feeling
        </p>

        <!-- Controls -->
        <div class="flex items-center space-x-6 mb-8">
            <!-- Mic Button -->
            <button class="w-20 h-20 rounded-full bg-blue-600 hover:bg-blue-700 flex items-center justify-center shadow-2xl transform hover:scale-105 transition-all">
                <span class="text-white text-2xl">🎤</span>
            </button>

            <!-- Camera Button -->
            <button class="w-12 h-12 rounded-full glass-card hover:bg-gray-600 flex items-center justify-center transition-all">
                <span class="text-blue-400 text-lg">📷</span>
            </button>
        </div>

        <!-- Text Input -->
        <div class="w-full max-w-md flex items-center space-x-3">
            <input 
                type="text" 
                placeholder="Chat with Lyxara about your health..."
                class="flex-1 glass-card rounded-2xl px-4 py-3 text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
            <button class="w-10 h-10 rounded-full flex items-center justify-center">
                <span class="text-blue-400 text-lg">✈️</span>
            </button>
        </div>
    </div>

    <!-- Tab Bar -->
    <div class="glass-card border-t border-gray-700 p-4 mt-8">
        <div class="flex justify-around">
            <button class="flex flex-col items-center space-y-1 text-blue-400">
                <span class="text-xl">🎙️</span>
                <span class="text-xs font-semibold">Agent</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-500">
                <span class="text-xl">💬</span>
                <span class="text-xs">Inbox</span>
            </button>
        </div>
    </div>

    <!-- Connection Status -->
    <div class="fixed top-4 right-4">
        <div class="flex items-center space-x-2 glass-card px-3 py-2 rounded-full">
            <div class="w-2 h-2 rounded-full bg-green-400 pulse-animation"></div>
            <span class="text-xs text-gray-300">Connected</span>
        </div>
    </div>

    <script>
        // Simple interaction demo
        const micButton = document.querySelector('.bg-blue-600');
        const statusText = document.querySelector('.text-gray-300');
        
        let isRecording = false;
        
        micButton.addEventListener('click', () => {
            isRecording = !isRecording;
            if (isRecording) {
                micButton.innerHTML = '<span class="text-white text-2xl">⏹️</span>';
                micButton.classList.remove('bg-blue-600');
                micButton.classList.add('bg-red-600');
                statusText.textContent = 'Listening... speak freely';
                
                // Auto stop after 3 seconds for demo
                setTimeout(() => {
                    micButton.click();
                    statusText.textContent = 'Lyxara is analyzing...';
                    setTimeout(() => {
                        statusText.textContent = 'Tap to share how you\'re feeling';
                    }, 2000);
                }, 3000);
            } else {
                micButton.innerHTML = '<span class="text-white text-2xl">🎤</span>';
                micButton.classList.remove('bg-red-600');
                micButton.classList.add('bg-blue-600');
            }
        });
    </script>
</body>
</html>