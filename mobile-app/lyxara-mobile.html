<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lyxara - AI Health Companion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        * {
            -webkit-tap-highlight-color: transparent;
        }
        
        body { 
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            overflow: hidden;
        }
        
        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1e293b, #0f172a);
            border-radius: 50px;
            padding: 8px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
            position: relative;
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #111827 0%, #1f2937 100%);
            border-radius: 42px;
            overflow: hidden;
            position: relative;
        }
        
        .glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .glass-strong {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }
        
        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #000;
            border-radius: 0 0 15px 15px;
            z-index: 100;
        }
        
        .status-bar {
            position: absolute;
            top: 12px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 90;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .visualizer-canvas {
            border-radius: 50%;
            filter: blur(0.5px);
            opacity: 0.9;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }
        
        .float {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .glow {
            filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.4));
        }
        
        .button-active {
            background: rgba(96, 165, 250, 0.2);
            border-color: rgba(96, 165, 250, 0.4);
            transform: scale(0.95);
        }
        
        .icon-wrapper {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .icon-wrapper:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-8">
    
    <div class="phone-frame">
        <!-- Notch -->
        <div class="notch"></div>
        
        <!-- Screen -->
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="flex items-center space-x-1">
                    <span>9:41</span>
                </div>
                <div class="flex items-center space-x-1">
                    <div class="flex space-x-1">
                        <div class="w-1 h-1 bg-white rounded-full pulse"></div>
                        <div class="w-1 h-1 bg-white rounded-full pulse" style="animation-delay: 0.2s;"></div>
                        <div class="w-1 h-1 bg-white rounded-full pulse" style="animation-delay: 0.4s;"></div>
                    </div>
                    <div class="w-6 h-3 border border-white rounded-sm">
                        <div class="w-4 h-1 bg-green-400 rounded-sm m-0.5"></div>
                    </div>
                </div>
            </div>
            
            <!-- Content -->
            <div class="flex flex-col h-full pt-12">
                
                <!-- Header -->
                <div class="glass-strong rounded-3xl mx-4 mt-8 mb-6 p-4">
                    <div class="flex items-center justify-center mb-2">
                        <div class="w-8 h-8 mr-3 glow">
                            <i data-lucide="brain-circuit" class="w-8 h-8 text-blue-400"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-white">Lyxara</h1>
                    </div>
                    <p class="text-center text-gray-300 text-sm">Your AI Health Companion</p>
                </div>

                <!-- Visualizer Section -->
                <div class="flex-1 flex flex-col items-center justify-center px-6">
                    <div class="relative mb-6">
                        <div class="glass rounded-full p-4 float">
                            <canvas id="visualizer" width="240" height="240" class="visualizer-canvas"></canvas>
                        </div>
                        
                        <!-- Connection Status -->
                        <div class="absolute -top-2 -right-2 glass rounded-full px-3 py-1">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 rounded-full bg-green-400 pulse"></div>
                                <span class="text-xs text-gray-300">Live</span>
                            </div>
                        </div>
                    </div>

                    <!-- Status Text -->
                    <p class="text-center text-white mb-8 text-lg font-medium" id="statusText">
                        Tap to share how you're feeling
                    </p>

                    <!-- Controls -->
                    <div class="flex items-center space-x-6 mb-8">
                        <!-- Mic Button -->
                        <button id="micButton" class="glass-strong rounded-full p-6 icon-wrapper transition-all duration-300 transform hover:scale-105 active:scale-95">
                            <i data-lucide="mic" class="w-8 h-8 text-blue-400"></i>
                        </button>

                        <!-- Camera Button -->
                        <button class="glass rounded-full p-4 icon-wrapper transition-all duration-300 transform hover:scale-105 active:scale-95">
                            <i data-lucide="camera" class="w-6 h-6 text-blue-400"></i>
                        </button>
                        
                        <!-- Settings Button -->
                        <button class="glass rounded-full p-4 icon-wrapper transition-all duration-300 transform hover:scale-105 active:scale-95">
                            <i data-lucide="settings" class="w-6 h-6 text-gray-400"></i>
                        </button>
                    </div>

                    <!-- Text Input -->
                    <div class="w-full flex items-center space-x-3 px-2">
                        <div class="flex-1 glass rounded-3xl px-4 py-3">
                            <input 
                                type="text" 
                                placeholder="Chat with Lyxara about your health..."
                                class="w-full bg-transparent text-white placeholder-gray-400 focus:outline-none text-sm"
                                id="messageInput"
                            />
                        </div>
                        <button id="sendButton" class="glass rounded-full p-3 icon-wrapper transition-all duration-300 transform hover:scale-105 active:scale-95">
                            <i data-lucide="send" class="w-5 h-5 text-blue-400"></i>
                        </button>
                    </div>
                </div>

                <!-- Tab Bar -->
                <div class="glass-strong mx-4 mb-6 rounded-3xl p-4">
                    <div class="flex justify-around">
                        <button class="flex flex-col items-center space-y-1 p-2 rounded-2xl bg-blue-500/20 border border-blue-500/30">
                            <i data-lucide="activity" class="w-6 h-6 text-blue-400"></i>
                            <span class="text-xs font-semibold text-blue-400">Agent</span>
                        </button>
                        <button class="flex flex-col items-center space-y-1 p-2 rounded-2xl">
                            <i data-lucide="message-circle" class="w-6 h-6 text-gray-500"></i>
                            <span class="text-xs text-gray-500">Inbox</span>
                        </button>
                        <button class="flex flex-col items-center space-y-1 p-2 rounded-2xl">
                            <i data-lucide="heart" class="w-6 h-6 text-gray-500"></i>
                            <span class="text-xs text-gray-500">Health</span>
                        </button>
                        <button class="flex flex-col items-center space-y-1 p-2 rounded-2xl">
                            <i data-lucide="user" class="w-6 h-6 text-gray-500"></i>
                            <span class="text-xs text-gray-500">Profile</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Joy Division Visualizer (Enhanced for mobile)
        class LyxaraVisualizer {
            constructor(canvas) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.width = canvas.width;
                this.height = canvas.height;
                
                this.centerX = this.width / 2;
                this.centerY = this.height / 2;
                this.maxRadius = Math.min(this.width, this.height) * 0.35;
                
                this.numRings = 10;
                this.numPoints = 96;
                this.time = 0;
                
                this.frequencyData = new Array(48).fill(0);
                this.smoothedData = new Array(48).fill(0);
                
                this.agentState = 'idle';
                this.audioLevel = 0;
                
                this.animate();
            }
            
            setAgentState(state) {
                this.agentState = state;
            }
            
            generateFrequencyData() {
                const baseAmplitude = this.getBaseAmplitude();
                const noiseLevel = this.getNoiseLevel();
                
                for (let i = 0; i < this.frequencyData.length; i++) {
                    let value = 0;
                    
                    switch (this.agentState) {
                        case 'listening':
                            value = (this.audioLevel * 0.9 + Math.random() * 0.1) * 
                                    (1 + Math.sin(this.time * 4 + i * 0.15) * 0.4) *
                                    Math.pow(i / this.frequencyData.length, 0.7);
                            break;
                            
                        case 'thinking':
                            const wave1 = Math.sin(this.time * 2.3 + i * 0.18);
                            const wave2 = Math.sin(this.time * 3.7 + i * 0.12);
                            const wave3 = Math.sin(this.time * 1.1 + i * 0.25);
                            value = baseAmplitude * 
                                    (0.6 + 0.4 * wave1) *
                                    (0.7 + 0.3 * wave2) *
                                    (0.8 + 0.2 * wave3) *
                                    Math.exp(-Math.pow(i - 24, 2) / 300);
                            break;
                            
                        case 'speaking':
                            const f1 = Math.exp(-Math.pow(i - 8, 2) / 60);
                            const f2 = Math.exp(-Math.pow(i - 20, 2) / 80);
                            const f3 = Math.exp(-Math.pow(i - 36, 2) / 100);
                            value = baseAmplitude * 0.9 * 
                                    (f1 + f2 * 0.8 + f3 * 0.6) *
                                    (0.7 + 0.3 * Math.sin(this.time * 12 + i * 0.4));
                            break;
                            
                        default: // idle
                            value = noiseLevel * 0.15 * 
                                    (0.5 + 0.5 * Math.sin(this.time * 0.8 + i * 0.08));
                    }
                    
                    value += (Math.random() - 0.5) * noiseLevel * 0.08;
                    this.smoothedData[i] = this.smoothedData[i] * 0.85 + value * 0.15;
                    this.frequencyData[i] = Math.max(0, Math.min(1, this.smoothedData[i]));
                }
                
                if (this.agentState === 'listening') {
                    this.audioLevel = 0.3 + 0.7 * (0.5 + 0.5 * Math.sin(this.time * 3));
                } else {
                    this.audioLevel *= 0.95;
                }
            }
            
            getBaseAmplitude() {
                switch (this.agentState) {
                    case 'listening': return 0.7 + this.audioLevel * 0.3;
                    case 'thinking': return 0.8;
                    case 'speaking': return 0.9;
                    default: return 0.25;
                }
            }
            
            getNoiseLevel() {
                switch (this.agentState) {
                    case 'listening': return 0.4;
                    case 'thinking': return 0.3;
                    case 'speaking': return 0.5;
                    default: return 0.15;
                }
            }

            getEmotionalColor(ring) {
                const ringPhase = (ring / this.numRings) * Math.PI * 2;
                const timePhase = this.time * 0.5;

                switch (this.agentState) {
                    case 'idle':
                        return {
                            r: Math.floor(96 + 60 * Math.sin(timePhase + ringPhase)),
                            g: Math.floor(165 + 40 * Math.sin(timePhase + ringPhase + 1)),
                            b: Math.floor(250 + 5 * Math.sin(timePhase + ringPhase + 2))
                        };

                    case 'listening':
                        return {
                            r: Math.floor(34 + 40 * Math.sin(timePhase + ringPhase + 1)),
                            g: Math.floor(197 + 50 * Math.sin(timePhase + ringPhase)),
                            b: Math.floor(94 + 60 * Math.sin(timePhase + ringPhase + 0.5))
                        };

                    case 'thinking':
                        return {
                            r: Math.floor(251 + 4 * Math.sin(timePhase + ringPhase)),
                            g: Math.floor(191 + 40 * Math.sin(timePhase + ringPhase + 0.8)),
                            b: Math.floor(36 + 40 * Math.sin(timePhase + ringPhase + 1.5))
                        };

                    case 'speaking':
                        return {
                            r: Math.floor(168 + 60 * Math.sin(timePhase + ringPhase)),
                            g: Math.floor(85 + 80 * Math.sin(timePhase + ringPhase + 1)),
                            b: Math.floor(247 + 8 * Math.sin(timePhase + ringPhase + 0.5))
                        };

                    default:
                        return { r: 96, g: 165, b: 250 };
                }
            }
            
            drawFrequencyRings() {
                const ringSpacing = this.maxRadius / this.numRings;

                for (let ring = 0; ring < this.numRings; ring++) {
                    const baseRadius = ringSpacing * (ring + 1);
                    const opacity = 0.8 - (ring / this.numRings) * 0.5;
                    const strokeWeight = Math.max(0.8, 2.0 - ring * 0.12);

                    const color = this.getEmotionalColor(ring);
                    this.ctx.strokeStyle = `rgba(${color.r}, ${color.g}, ${color.b}, ${opacity})`;
                    this.ctx.lineWidth = strokeWeight;
                    this.ctx.lineCap = 'round';
                    this.ctx.lineJoin = 'round';
                    
                    this.ctx.beginPath();
                    let firstX, firstY;

                    for (let i = 0; i <= this.numPoints; i++) {
                        const angle = (i / this.numPoints) * Math.PI * 2;
                        const freqIndex = Math.floor((i / this.numPoints) * this.frequencyData.length);
                        const amplitude = this.frequencyData[freqIndex] || 0;

                        const ringMod = 1 + Math.sin(this.time * 2 + ring * 0.7) * 0.08;
                        const radiusVar = amplitude * 25 * ringMod;

                        const phaseOffset = ring * 0.3;
                        const finalRadius = baseRadius + radiusVar +
                                           Math.sin(this.time * 1.5 + angle * 3 + phaseOffset) * 1.5;

                        const x = this.centerX + Math.cos(angle) * finalRadius;
                        const y = this.centerY + Math.sin(angle) * finalRadius;

                        if (i === 0) {
                            this.ctx.moveTo(x, y);
                            firstX = x;
                            firstY = y;
                        } else {
                            this.ctx.lineTo(x, y);
                        }
                    }

                    this.ctx.lineTo(firstX, firstY);
                    this.ctx.stroke();
                }
            }
            
            drawCenterElement() {
                const pulseSize = 4 + this.getBaseAmplitude() * 8;
                const glowSize = pulseSize * 2.5;
                const centerColor = this.getEmotionalColor(0);

                const gradient = this.ctx.createRadialGradient(
                    this.centerX, this.centerY, 0,
                    this.centerX, this.centerY, glowSize
                );
                gradient.addColorStop(0, `rgba(${centerColor.r}, ${centerColor.g}, ${centerColor.b}, ${0.6 + this.getBaseAmplitude() * 0.4})`);
                gradient.addColorStop(0.5, `rgba(${centerColor.r}, ${centerColor.g}, ${centerColor.b}, ${0.3 + this.getBaseAmplitude() * 0.2})`);
                gradient.addColorStop(1, `rgba(${centerColor.r}, ${centerColor.g}, ${centerColor.b}, 0)`);

                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(this.centerX, this.centerY, glowSize, 0, Math.PI * 2);
                this.ctx.fill();

                this.ctx.fillStyle = `rgba(${centerColor.r}, ${centerColor.g}, ${centerColor.b}, 0.9)`;
                this.ctx.beginPath();
                this.ctx.arc(this.centerX, this.centerY, pulseSize, 0, Math.PI * 2);
                this.ctx.fill();
            }
            
            animate() {
                this.time += 0.016;
                
                this.ctx.fillStyle = 'rgba(17, 24, 39, 0.8)';
                this.ctx.fillRect(0, 0, this.width, this.height);
                
                this.generateFrequencyData();
                this.drawFrequencyRings();
                this.drawCenterElement();
                
                requestAnimationFrame(() => this.animate());
            }
        }
        
        // Initialize visualizer
        const canvas = document.getElementById('visualizer');
        const visualizer = new LyxaraVisualizer(canvas);
        
        // UI Interactions
        const micButton = document.getElementById('micButton');
        const statusText = document.getElementById('statusText');
        const sendButton = document.getElementById('sendButton');
        const messageInput = document.getElementById('messageInput');
        
        let isRecording = false;
        let currentState = 'idle';
        
        // Mic button interaction
        micButton.addEventListener('click', () => {
            isRecording = !isRecording;
            
            if (isRecording) {
                currentState = 'listening';
                visualizer.setAgentState('listening');
                micButton.innerHTML = '<i data-lucide="mic-off" class="w-8 h-8 text-red-400"></i>';
                micButton.classList.add('button-active');
                statusText.textContent = 'Listening... speak freely';
                
                // Auto stop after 4 seconds for demo
                setTimeout(() => {
                    if (isRecording) {
                        micButton.click();
                        currentState = 'thinking';
                        visualizer.setAgentState('thinking');
                        statusText.textContent = 'Lyxara is analyzing...';
                        
                        setTimeout(() => {
                            currentState = 'speaking';
                            visualizer.setAgentState('speaking');
                            statusText.textContent = 'Lyxara is responding...';
                            
                            setTimeout(() => {
                                currentState = 'idle';
                                visualizer.setAgentState('idle');
                                statusText.textContent = 'Tap to share how you\'re feeling';
                            }, 3000);
                        }, 2000);
                    }
                }, 4000);
            } else {
                isRecording = false;
                micButton.innerHTML = '<i data-lucide="mic" class="w-8 h-8 text-blue-400"></i>';
                micButton.classList.remove('button-active');
            }
            
            lucide.createIcons();
        });
        
        // Send message interaction
        sendButton.addEventListener('click', () => {
            const message = messageInput.value.trim();
            if (message) {
                messageInput.value = '';
                currentState = 'thinking';
                visualizer.setAgentState('thinking');
                statusText.textContent = 'Lyxara is analyzing...';
                
                setTimeout(() => {
                    currentState = 'speaking';
                    visualizer.setAgentState('speaking');
                    statusText.textContent = 'Lyxara is responding...';
                    
                    setTimeout(() => {
                        currentState = 'idle';
                        visualizer.setAgentState('idle');
                        statusText.textContent = 'Tap to share how you\'re feeling';
                    }, 3000);
                }, 1500);
            }
        });
        
        // Enter key support
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendButton.click();
            }
        });
        
        // Auto demo cycle (optional)
        let demoMode = false;
        setTimeout(() => {
            demoMode = true;
            // Uncomment to enable auto demo
            // setInterval(() => {
            //     if (currentState === 'idle' && demoMode) {
            //         micButton.click();
            //     }
            // }, 8000);
        }, 10000);
    </script>
</body>
</html>