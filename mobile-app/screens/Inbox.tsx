import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import io from 'socket.io-client';

const SERVER_URL = 'http://localhost:3000';

interface Message {
  id: string;
  from: string;
  subtype: string;
  payload: any;
  timestamp: string;
}

const Inbox: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [socket, setSocket] = useState<any>(null);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server');
    });

    newSocket.on('newMessage', (message: Message) => {
      setMessages(prev => [...prev, message]);
    });

    // Load initial messages
    loadMessages();

    return () => newSocket.close();
  }, []);

  const loadMessages = async () => {
    try {
      const response = await fetch(`${SERVER_URL}/messages`);
      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Failed to load messages:', error);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getMessageIcon = (message: Message) => {
    switch (message.subtype) {
      case 'symptom':
        return 'medical';
      case 'image':
        return 'camera';
      case 'text':
        return message.from === 'doctor' ? 'person' : 'chatbubble';
      case 'recipe':
        return 'medical';
      default:
        return 'chatbubble';
    }
  };

  const getMessageColor = (message: Message) => {
    switch (message.subtype) {
      case 'symptom':
        return '#FF6B6B';
      case 'image':
        return '#4ECDC4';
      case 'recipe':
        return '#45B7D1';
      default:
        return message.from === 'doctor' ? '#9B59B6' : '#95A5A6';
    }
  };

  const getMessageText = (message: Message) => {
    switch (message.subtype) {
      case 'symptom':
        return message.payload.text || 'Symptom recorded';
      case 'image':
        return message.payload.caption || 'Image shared';
      case 'text':
        return message.payload.text;
      case 'recipe':
        return `Prescription: ${message.payload.medication} ${message.payload.dosage}`;
      default:
        return 'Message';
    }
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <TouchableOpacity style={styles.messageItem}>
      <View style={[styles.iconContainer, { backgroundColor: getMessageColor(item) }]}>
        <Ionicons 
          name={getMessageIcon(item) as any} 
          size={20} 
          color="white" 
        />
      </View>
      
      <View style={styles.messageContent}>
        <View style={styles.messageHeader}>
          <Text style={styles.senderName}>
            {item.from === 'doctor' ? '👩‍⚕️ Care Team' : 'You'}
          </Text>
          <Text style={styles.timestamp}>
            {formatTime(item.timestamp)}
          </Text>
        </View>
        
        <Text style={styles.messageText} numberOfLines={2}>
          {getMessageText(item)}
        </Text>
        
        {item.subtype === 'image' && item.payload.url && (
          <Image 
            source={{ uri: `${SERVER_URL}${item.payload.url}` }}
            style={styles.messageImage}
          />
        )}
        
        {item.subtype === 'symptom' && item.payload.confidence && (
          <Text style={styles.confidenceText}>
            Confidence: {(item.payload.confidence * 100).toFixed(0)}%
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>💬 Lyxara Inbox</Text>
        <Text style={styles.subtitle}>{messages.length} conversations with your care team</Text>
      </View>

      {messages.length === 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="chatbubbles-outline" size={64} color="#6B7280" />
          <Text style={styles.emptyText}>Your inbox is empty</Text>
          <Text style={styles.emptySubtext}>
            Messages from your care team and Lyxara will appear here
          </Text>
        </View>
      ) : (
        <FlatList
          data={messages.slice().reverse()} // Show newest first
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#111827',
    paddingTop: 60,
  },
  header: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#F9FAFB',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#9CA3AF',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  messageItem: {
    flexDirection: 'row',
    backgroundColor: '#1F2937',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#374151',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  messageContent: {
    flex: 1,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  senderName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#F9FAFB',
  },
  timestamp: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  messageText: {
    fontSize: 14,
    color: '#D1D5DB',
    lineHeight: 20,
  },
  messageImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginTop: 8,
  },
  confidenceText: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
  },
});

export default Inbox;
