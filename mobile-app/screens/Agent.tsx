import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import * as ImagePicker from 'expo-image-picker';
import io from 'socket.io-client';
import ReactiveBlob from '../components/ReactiveBlob';
import JoyDivisionVisualizer from '../components/JoyDivisionVisualizer';

const SERVER_URL = 'http://localhost:3000';

type BlobState = 'idle' | 'recording' | 'thinking';

const Agent: React.FC = () => {
  const [blobState, setBlobState] = useState<BlobState>('idle');
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [textInput, setTextInput] = useState('');
  const [socket, setSocket] = useState<any>(null);
  const [audioLevel, setAudioLevel] = useState(0);
  const [agentState, setAgentState] = useState<'idle' | 'listening' | 'thinking' | 'speaking' | 'emergency'>('idle');
  const [emotion, setEmotion] = useState<'neutral' | 'positive' | 'negative' | 'concerned'>('neutral');
  const [audioRecordingStatus, setAudioRecordingStatus] = useState<Audio.RecordingStatus | null>(null);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server:', newSocket.id);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
    });

    newSocket.on('reconnect', () => {
      console.log('Reconnected to server');
    });

    newSocket.on('newMessage', (data: any) => {
      console.log('New message received:', data);
      
      // Handle different message types
      if (data.message) {
        // Handle doctor messages
        if (data.message.from === 'doctor') {
          // Change agent state to show doctor is responding
          setAgentState('idle'); // Reset any previous state
          
          // Display doctor messages
          const doctorMessage = data.message.payload.text;
          setTimeout(() => {
            Alert.alert(
              '👩‍⚕️ Doctor Message',
              doctorMessage,
              [{ text: 'OK', style: 'default' }]
            );
          }, 500);
        }
        
        // Handle AI companion messages
        else if (data.message.from === 'ai_companion') {
          // If it's an AI response, analyze for emotional content
          const aiText = data.message.payload.text || '';
          
          // Simple emotion detection based on keywords
          if (/emergency|urgent|critical|immediate|severe/i.test(aiText)) {
            setAgentState('emergency');
            setEmotion('concerned');
          } else if (/happy|great|excellent|good news|improving/i.test(aiText)) {
            setEmotion('positive');
          } else if (/concerned|worry|attention|careful/i.test(aiText)) {
            setEmotion('concerned');
          } else if (/bad|worse|declined|negative|problem/i.test(aiText)) {
            setEmotion('negative');
          } else {
            // Default
            setEmotion('neutral');
          }
        }
        
        // Handle system messages
        else if (data.message.from === 'system') {
          console.log('System message:', data.message.payload);
        }
      }
      
      // Handle temporal context updates
      if (data.temporalLayer) {
        console.log('Temporal layer updated:', data.temporalLayer);
      }
    });

    newSocket.on('audioResponse', async (data: any) => {
      console.log('Audio response received:', data);
      
      // Handle audio playback from doctor or system
      if (data.audioUrl) {
        try {
          // Update agent state to speaking
          setAgentState('speaking');
          
          // Play the audio
          const { sound } = await Audio.Sound.createAsync(
            { uri: `${SERVER_URL}${data.audioUrl}` },
            { shouldPlay: true }
          );
          
          // Listen for playback status updates
          sound.setOnPlaybackStatusUpdate((status) => {
            if (status.isLoaded && !status.isBuffering && status.didJustFinish) {
              // Reset state when audio finishes playing
              setAgentState('idle');
              sound.unloadAsync();
            }
          });
        } catch (error) {
          console.error('Error playing audio response:', error);
          setAgentState('idle');
        }
      }
    });
    
    // Handle safety alerts (for emergency conditions)
    newSocket.on('safetyAlert', (data: any) => {
      console.log('Safety alert received:', data);
      
      if (data.level === 'red' || data.level === 'orange') {
        setAgentState('emergency');
        setEmotion('concerned');
        
        // Show critical alert
        Alert.alert(
          '⚠️ Medical Alert',
          data.message || 'Important medical alert',
          [{ text: 'Acknowledge', style: 'destructive' }]
        );
      }
    });

    return () => newSocket.close();
  }, [blobState]);

  const startRecording = async () => {
    try {
      const permission = await Audio.requestPermissionsAsync();
      if (permission.status !== 'granted') {
        Alert.alert('Permission required', 'Please grant microphone permission');
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      setBlobState('recording');
      setAgentState('listening');
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY,
        (status) => {
          setAudioRecordingStatus(status);
          // Update audio level for visualizer based on metering
          if (status.metering && status.metering > -50) {
            // Convert dB metering to a 0-10 scale for visualization
            const level = Math.max(0, (status.metering + 50) / 5);
            setAudioLevel(level);
          }
        },
        100 // Update every 100ms
      );
      setRecording(recording);

      // Auto-stop after 5 seconds for demo
      setTimeout(() => {
        if (recording) {
          stopRecording();
        }
      }, 5000);
    } catch (err) {
      console.error('Failed to start recording', err);
      setBlobState('idle');
      setAgentState('idle');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    setBlobState('thinking');
    setAgentState('thinking');
    setAudioLevel(0); // Reset audio level
    setRecording(null);

    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      
      if (uri) {
        await uploadAudio(uri);
      }
    } catch (error) {
      console.error('Failed to stop recording', error);
      setBlobState('idle');
      setAgentState('idle');
    }
  };

  const uploadAudio = async (uri: string) => {
    try {
      // First, upload audio to Watson Speech-to-Text service
      const formData = new FormData();
      formData.append('audio', {
        uri,
        type: 'audio/mp4',
        name: 'recording.mp4',
      } as any);

      const sttResponse = await fetch(`${SERVER_URL}/watson/stt`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      let transcription = '';
      
      if (sttResponse.ok) {
        const sttResult = await sttResponse.json();
        transcription = sttResult.transcription || 'Unable to transcribe audio';
        console.log('Transcription result:', sttResult);
      } else {
        // Fallback to mock if Watson fails
        const mockTranscriptions = [
          "I have been experiencing severe headaches for the past three days",
          "My stomach has been hurting and I feel nauseous",
          "I'm having trouble sleeping and feel very anxious",
          "The medication you prescribed isn't helping much",
          "I feel dizzy when I stand up quickly"
        ];
        transcription = mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];
        console.log('Using mock transcription due to STT failure');
      }

      // Send transcription to LLM chat
      const response = await fetch(`${SERVER_URL}/chat/patient/default-patient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: transcription,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Voice message processed:', result);

        // Generate speech for AI response using Watson TTS
        const aiResponseText = result.aiResponse.payload.text;
        
        try {
          const ttsResponse = await fetch(`${SERVER_URL}/watson/tts`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              text: aiResponseText,
              voice: 'en-US_AllisonV3Voice',
              patientId: 'default-patient'
            }),
          });

          if (ttsResponse.ok) {
            const ttsResult = await ttsResponse.json();
            console.log('TTS audio generated:', ttsResult.audioUrl);
            
            // Play the audio response with Audio.Sound
            try {
              const { sound } = await Audio.Sound.createAsync(
                { uri: `${SERVER_URL}${ttsResult.audioUrl}` },
                { shouldPlay: true }
              );
              
              // Update agent state to speaking while audio plays
              setAgentState('speaking');
              
              // Listen for playback status updates
              sound.setOnPlaybackStatusUpdate((status) => {
                if (status.isLoaded && !status.isBuffering && status.didJustFinish) {
                  // Reset state when audio finishes playing
                  setAgentState('idle');
                  setEmotion('neutral');
                  sound.unloadAsync();
                }
              });
              
              // Show minimal notification that doesn't interrupt the audio experience
              Alert.alert(
                '🎤 Voice Message Processed',
                `You said: "${transcription}"`,
                [{ text: 'OK', style: 'default' }]
              );
            } catch (playError) {
              console.error('Audio playback error:', playError);
              Alert.alert(
                '🎤 Voice Message Processed',
                `You said: "${transcription}"\n\n🤖 AI Response: ${aiResponseText}\n\nCouldn't play audio response.`,
                [{ text: 'OK', style: 'default' }]
              );
            }
          } else {
            // Fallback to text-only response
            setTimeout(() => {
              Alert.alert(
                '🎤 Voice Message Processed',
                `You said: "${transcription}"\n\n🤖 AI Response: ${aiResponseText}`,
                [{ text: 'OK', style: 'default' }]
              );
            }, 1000);
          }
        } catch (ttsError) {
          console.error('TTS error:', ttsError);
          // Fallback to text-only response
          setTimeout(() => {
            Alert.alert(
              '🎤 Voice Message Processed',
              `You said: "${transcription}"\n\n🤖 AI Response: ${aiResponseText}`,
              [{ text: 'OK', style: 'default' }]
            );
          }, 1000);
        }
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Audio upload error:', error);
      Alert.alert('Error', 'Failed to process voice message');
    } finally {
      setBlobState('idle');
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      setBlobState('thinking');
      await uploadImage(result.assets[0].uri);
    }
  };

  const uploadImage = async (uri: string) => {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri,
        type: 'image/jpeg',
        name: 'image.jpg',
      } as any);

      const response = await fetch(`${SERVER_URL}/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Image uploaded successfully:', result);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      Alert.alert('Error', 'Failed to upload image');
    } finally {
      setBlobState('idle');
    }
  };

  const sendTextMessage = async () => {
    if (!textInput.trim()) return;

    setBlobState('thinking');

    try {
      // Use the new LLM chat endpoint
      const response = await fetch(`${SERVER_URL}/chat/patient/default-patient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: textInput.trim(),
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('AI Response received:', result.aiResponse.payload.text);
        setTextInput('');

        // Show AI response in an alert for demo
        setTimeout(() => {
          Alert.alert(
            '🤖 AI Companion Response',
            result.aiResponse.payload.text,
            [{ text: 'OK', style: 'default' }]
          );
        }, 1000);
      } else {
        throw new Error('Send failed');
      }
    } catch (error) {
      console.error('Text message error:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setBlobState('idle');
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoIcon}>🧠</Text>
          <Text style={styles.title}>Lyxara</Text>
        </View>
        <Text style={styles.subtitle}>Your AI Health Companion</Text>
      </View>
      
      <View style={styles.blobContainer}>
        <JoyDivisionVisualizer
          agentState={agentState}
          audioLevel={audioLevel}
          isRecording={blobState === 'recording'}
          isThinking={blobState === 'thinking'}
          isSpeaking={agentState === 'speaking'}
          isEmergency={agentState === 'emergency'}
          emotion={emotion}
        />
        
        <Text style={styles.stateText}>
          {agentState === 'idle' && 'Tap to share how you\'re feeling'}
          {agentState === 'listening' && 'Listening... speak freely'}
          {agentState === 'thinking' && 'Lyxara is analyzing...'}
          {agentState === 'speaking' && 'Lyxara is responding...'}
          {agentState === 'emergency' && '🚨 Urgent medical attention needed!'}
        </Text>
      </View>

      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.micButton, blobState === 'recording' && styles.recordingButton]}
          onPress={blobState === 'recording' ? stopRecording : startRecording}
          disabled={blobState === 'thinking'}
        >
          <Ionicons 
            name={blobState === 'recording' ? 'stop' : 'mic'} 
            size={32} 
            color="white" 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.imageButton}
          onPress={pickImage}
          disabled={blobState !== 'idle'}
        >
          <Ionicons name="camera" size={24} color="#60A5FA" />
        </TouchableOpacity>
      </View>

      <View style={styles.textInputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder="Chat with Lyxara about your health..."
          placeholderTextColor="#9CA3AF"
          value={textInput}
          onChangeText={setTextInput}
          multiline
        />
        <TouchableOpacity
          style={[styles.sendButton, !textInput.trim() && styles.sendButtonDisabled]}
          onPress={sendTextMessage}
          disabled={!textInput.trim()}
        >
          <Ionicons name="send" size={20} color={textInput.trim() ? '#007AFF' : '#C7C7CC'} />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#111827',
    paddingTop: 60,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 40,
    paddingVertical: 20,
    backgroundColor: 'rgba(31, 41, 55, 0.9)',
    borderRadius: 20,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: '#374151',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  logoIcon: {
    fontSize: 32,
    marginRight: 8,
  },
  title: {
    fontSize: 32,
    fontWeight: '800',
    color: '#60A5FA',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 17,
    color: '#9CA3AF',
    textAlign: 'center',
    fontWeight: '500',
  },
  blobContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  stateText: {
    fontSize: 16,
    color: '#D1D5DB',
    textAlign: 'center',
    marginTop: 20,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 20,
  },
  micButton: {
    width: 90,
    height: 90,
    borderRadius: 45,
    backgroundColor: '#3B82F6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  recordingButton: {
    backgroundColor: '#EF4444',
    shadowColor: '#EF4444',
  },
  imageButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#374151',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 10,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#4B5563',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#374151',
    color: '#F9FAFB',
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
});

export default Agent;
