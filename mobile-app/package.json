{"name": "lyxara-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start --web", "tunnel": "expo start --tunnel", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@expo/webpack-config": "^19.0.0", "expo": "~49.0.15", "expo-av": "~13.4.1", "expo-image-picker": "~14.3.2", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.6", "react-native-svg": "13.9.0", "react-native-web": "~0.19.6", "socket.io-client": "^4.7.5", "typescript": "^5.1.3", "@types/react": "~18.2.14"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}