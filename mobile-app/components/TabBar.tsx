import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface TabBarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const TabBar: React.FC<TabBarProps> = ({ activeTab, onTabChange }) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'agent' && styles.activeTab]}
        onPress={() => onTabChange('agent')}
      >
        <Ionicons 
          name="mic" 
          size={24} 
          color={activeTab === 'agent' ? '#60A5FA' : '#6B7280'} 
        />
        <Text style={[styles.tabText, activeTab === 'agent' && styles.activeTabText]}>
          Agent
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.tab, activeTab === 'inbox' && styles.activeTab]}
        onPress={() => onTabChange('inbox')}
      >
        <Ionicons 
          name="chatbubbles" 
          size={24} 
          color={activeTab === 'inbox' ? '#60A5FA' : '#6B7280'} 
        />
        <Text style={[styles.tabText, activeTab === 'inbox' && styles.activeTabText]}>
          Inbox
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#1F2937',
    borderTopWidth: 1,
    borderTopColor: '#374151',
    paddingBottom: 34, // Safe area for iPhone
    paddingTop: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  activeTab: {
    // Active tab styling handled by text/icon color
  },
  tabText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  activeTabText: {
    color: '#60A5FA',
    fontWeight: '600',
  },
});

export default TabBar;
