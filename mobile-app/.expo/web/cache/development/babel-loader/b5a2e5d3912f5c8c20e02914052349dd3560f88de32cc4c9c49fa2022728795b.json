{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useRef, useEffect, useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar JoyDivisionVisualizer = function JoyDivisionVisualizer(_ref) {\n  var _ref$isRecording = _ref.isRecording,\n    isRecording = _ref$isRecording === void 0 ? false : _ref$isRecording,\n    _ref$isThinking = _ref.isThinking,\n    isThinking = _ref$isThinking === void 0 ? false : _ref$isThinking,\n    _ref$isSpeaking = _ref.isSpeaking,\n    isSpeaking = _ref$isSpeaking === void 0 ? false : _ref$isSpeaking,\n    _ref$isEmergency = _ref.isEmergency,\n    isEmergency = _ref$isEmergency === void 0 ? false : _ref$isEmergency,\n    _ref$audioLevel = _ref.audioLevel,\n    audioLevel = _ref$audioLevel === void 0 ? 0 : _ref$audioLevel,\n    _ref$agentState = _ref.agentState,\n    agentState = _ref$agentState === void 0 ? 'idle' : _ref$agentState,\n    _ref$emotion = _ref.emotion,\n    emotion = _ref$emotion === void 0 ? 'neutral' : _ref$emotion;\n  var canvasRef = useRef(null);\n  var animationRef = useRef(null);\n  var _useState = useState(Dimensions.get('window')),\n    _useState2 = _slicedToArray(_useState, 2),\n    dimensions = _useState2[0],\n    setDimensions = _useState2[1];\n  var frequencyData = useRef(new Array(64).fill(0));\n  var smoothedData = useRef(new Array(64).fill(0));\n  var time = useRef(0);\n  useEffect(function () {\n    var subscription = Dimensions.addEventListener('change', function (_ref2) {\n      var window = _ref2.window;\n      setDimensions(window);\n    });\n    return function () {\n      return subscription == null ? void 0 : subscription.remove();\n    };\n  }, []);\n  useEffect(function () {\n    var canvas = canvasRef.current;\n    if (!canvas) return;\n    var ctx = canvas.getContext('2d');\n    var width = dimensions.width,\n      height = dimensions.height;\n    canvas.width = width;\n    canvas.height = height;\n    var centerX = width / 2;\n    var centerY = height / 2;\n    var maxRadius = Math.min(width, height) * 0.4;\n    var numRings = 12;\n    var numPoints = 128;\n    var _animate = function animate() {\n      time.current += 0.016;\n      ctx.fillStyle = '#0a0a0a';\n      ctx.fillRect(0, 0, width, height);\n      generateFrequencyData();\n      drawFrequencyRings(ctx, centerX, centerY, maxRadius, numRings, numPoints);\n      drawCenterPulse(ctx, centerX, centerY);\n      drawStateIndicator(ctx, centerX, centerY, maxRadius);\n      animationRef.current = requestAnimationFrame(_animate);\n    };\n    _animate();\n    return function () {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions, isRecording, isThinking, isSpeaking, audioLevel, agentState]);\n  var generateFrequencyData = function generateFrequencyData() {\n    var baseAmplitude = getBaseAmplitude();\n    var noiseLevel = getNoiseLevel();\n    for (var i = 0; i < frequencyData.current.length; i++) {\n      var value = 0;\n      switch (agentState) {\n        case 'listening':\n          value = (audioLevel * 0.8 + Math.random() * 0.2) * (1 + Math.sin(time.current * 3 + i * 0.1) * 0.3) * (i / frequencyData.current.length);\n          break;\n        case 'thinking':\n          value = baseAmplitude * (0.5 + 0.5 * Math.sin(time.current * 2 + i * 0.2)) * (0.7 + 0.3 * Math.sin(time.current * 5 + i * 0.05)) * Math.exp(-Math.abs(i - 32) / 20);\n          break;\n        case 'speaking':\n          var formant1 = Math.exp(-Math.pow(i - 8, 2) / 50);\n          var formant2 = Math.exp(-Math.pow(i - 24, 2) / 80);\n          var formant3 = Math.exp(-Math.pow(i - 45, 2) / 100);\n          value = baseAmplitude * 0.8 * (formant1 + formant2 * 0.7 + formant3 * 0.5) * (0.8 + 0.2 * Math.sin(time.current * 8 + i * 0.3));\n          break;\n        default:\n          value = noiseLevel * 0.1 * (0.5 + 0.5 * Math.sin(time.current * 0.5 + i * 0.1));\n      }\n      value += (Math.random() - 0.5) * noiseLevel * 0.1;\n      smoothedData.current[i] = smoothedData.current[i] * 0.85 + value * 0.15;\n      frequencyData.current[i] = Math.max(0, Math.min(1, smoothedData.current[i]));\n    }\n  };\n  var getBaseAmplitude = function getBaseAmplitude() {\n    switch (agentState) {\n      case 'listening':\n        return 0.6 + audioLevel * 0.4;\n      case 'thinking':\n        return 0.7;\n      case 'speaking':\n        return 0.8;\n      default:\n        return 0.2;\n    }\n  };\n  var getNoiseLevel = function getNoiseLevel() {\n    switch (agentState) {\n      case 'listening':\n        return 0.3;\n      case 'thinking':\n        return 0.2;\n      case 'speaking':\n        return 0.4;\n      default:\n        return 0.1;\n    }\n  };\n  var getEmotionalColor = function getEmotionalColor(ring) {\n    var ringPhase = ring / 12 * Math.PI * 2;\n    var timePhase = time.current * 0.5;\n    switch (agentState) {\n      case 'idle':\n        return `rgba(${Math.floor(100 + 80 * Math.sin(timePhase + ringPhase))}, ${Math.floor(150 + 60 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase + 2))}, `;\n      case 'listening':\n        return `rgba(${Math.floor(80 + 60 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(180 + 75 * Math.sin(timePhase + ringPhase))}, ${Math.floor(140 + 70 * Math.sin(timePhase + ringPhase + 0.5))}, `;\n      case 'thinking':\n        return `rgba(${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase))}, ${Math.floor(160 + 70 * Math.sin(timePhase + ringPhase + 0.8))}, ${Math.floor(100 + 50 * Math.sin(timePhase + ringPhase + 1.5))}, `;\n      case 'speaking':\n        return `rgba(${Math.floor(180 + 60 * Math.sin(timePhase + ringPhase))}, ${Math.floor(130 + 70 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase + 0.5))}, `;\n      case 'emergency':\n        return `rgba(${Math.floor(220 + 35 * Math.sin(timePhase * 3 + ringPhase))}, ${Math.floor(100 + 80 * Math.sin(timePhase * 2 + ringPhase + 1))}, ${Math.floor(80 + 60 * Math.sin(timePhase + ringPhase + 2))}, `;\n      default:\n        return 'rgba(255, 255, 255, ';\n    }\n  };\n  var drawFrequencyRings = function drawFrequencyRings(ctx, centerX, centerY, maxRadius, numRings, numPoints) {\n    var ringSpacing = maxRadius / numRings;\n    for (var ring = 0; ring < numRings; ring++) {\n      var baseRadius = ringSpacing * (ring + 1);\n      var opacity = 1 - ring / numRings * 0.7;\n      var colorBase = getEmotionalColor(ring);\n      ctx.strokeStyle = `${colorBase}${opacity})`;\n      ctx.lineWidth = Math.max(1, 3 - ring * 0.2);\n      ctx.lineCap = 'round';\n      ctx.lineJoin = 'round';\n      ctx.beginPath();\n      var firstX = void 0,\n        firstY = void 0;\n      for (var i = 0; i <= numPoints; i++) {\n        var angle = i / numPoints * Math.PI * 2;\n        var freqIndex = Math.floor(i / numPoints * frequencyData.current.length);\n        var amplitude = frequencyData.current[freqIndex] || 0;\n        var ringModulation = 1 + Math.sin(time.current * 1.5 + ring * 0.5) * 0.1;\n        var radiusVariation = amplitude * 30 * ringModulation;\n        var radius = baseRadius + radiusVariation;\n        var x = centerX + Math.cos(angle) * radius;\n        var y = centerY + Math.sin(angle) * radius;\n        if (i === 0) {\n          ctx.moveTo(x, y);\n          firstX = x;\n          firstY = y;\n        } else {\n          ctx.lineTo(x, y);\n        }\n      }\n      ctx.lineTo(firstX, firstY);\n      ctx.stroke();\n    }\n  };\n  var drawCenterPulse = function drawCenterPulse(ctx, centerX, centerY) {\n    var pulseRadius = 8 + getBaseAmplitude() * 12;\n    var pulseOpacity = 0.3 + getBaseAmplitude() * 0.4;\n    var gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, pulseRadius * 2);\n    gradient.addColorStop(0, `rgba(255, 255, 255, ${pulseOpacity})`);\n    gradient.addColorStop(0.5, `rgba(255, 255, 255, ${pulseOpacity * 0.3})`);\n    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n    ctx.fillStyle = gradient;\n    ctx.beginPath();\n    ctx.arc(centerX, centerY, pulseRadius * 2, 0, Math.PI * 2);\n    ctx.fill();\n    ctx.fillStyle = `rgba(255, 255, 255, ${0.8 + pulseOpacity * 0.2})`;\n    ctx.beginPath();\n    ctx.arc(centerX, centerY, pulseRadius, 0, Math.PI * 2);\n    ctx.fill();\n  };\n  var drawStateIndicator = function drawStateIndicator(ctx, centerX, centerY, maxRadius) {\n    var indicatorY = centerY + maxRadius + 40;\n    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';\n    ctx.font = '16px monospace';\n    ctx.textAlign = 'center';\n    var stateText = {\n      idle: '◦ READY',\n      listening: '● LISTENING',\n      thinking: '◐ THINKING',\n      speaking: '◑ SPEAKING'\n    }[agentState] || '◦ READY';\n    ctx.fillText(stateText, centerX, indicatorY);\n    var barWidth = 2;\n    var barSpacing = 3;\n    var numBars = 32;\n    var barsWidth = numBars * (barWidth + barSpacing) - barSpacing;\n    var startX = centerX - barsWidth / 2;\n    for (var i = 0; i < numBars; i++) {\n      var freqIndex = Math.floor(i / numBars * frequencyData.current.length);\n      var amplitude = frequencyData.current[freqIndex] || 0;\n      var barHeight = amplitude * 20;\n      var x = startX + i * (barWidth + barSpacing);\n      var y = indicatorY + 20;\n      ctx.fillStyle = `rgba(255, 255, 255, ${0.3 + amplitude * 0.5})`;\n      ctx.fillRect(x, y - barHeight, barWidth, barHeight);\n    }\n  };\n  return _jsx(View, {\n    style: {\n      flex: 1,\n      backgroundColor: '#0a0a0a'\n    },\n    children: _jsx(\"canvas\", {\n      ref: canvasRef,\n      style: {\n        width: '100%',\n        height: '100%',\n        backgroundColor: '#0a0a0a'\n      }\n    })\n  });\n};\nexport default JoyDivisionVisualizer;", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "View", "Dimensions", "jsx", "_jsx", "JoyDivisionVisualizer", "_ref", "_ref$isRecording", "isRecording", "_ref$isThinking", "isThinking", "_ref$isSpeaking", "isSpeaking", "_ref$isEmergency", "isEmergency", "_ref$audioLevel", "audioLevel", "_ref$agentState", "agentState", "_ref$emotion", "emotion", "canvasRef", "animationRef", "_useState", "get", "_useState2", "_slicedToArray", "dimensions", "setDimensions", "frequencyData", "Array", "fill", "smoothedData", "time", "subscription", "addEventListener", "_ref2", "window", "remove", "canvas", "current", "ctx", "getContext", "width", "height", "centerX", "centerY", "maxRadius", "Math", "min", "numRings", "numPoints", "animate", "fillStyle", "fillRect", "generateFrequencyData", "drawFrequencyRings", "drawCenterPulse", "drawStateIndicator", "requestAnimationFrame", "cancelAnimationFrame", "baseAmplitude", "getBaseAmplitude", "noiseLevel", "getNoiseLevel", "i", "length", "value", "random", "sin", "exp", "abs", "formant1", "pow", "formant2", "formant3", "max", "getEmotionalColor", "ring", "ringPhase", "PI", "timePhase", "floor", "ringSpacing", "baseRadius", "opacity", "colorBase", "strokeStyle", "lineWidth", "lineCap", "lineJoin", "beginPath", "firstX", "firstY", "angle", "freqIndex", "amplitude", "ringModulation", "radiusVariation", "radius", "x", "cos", "y", "moveTo", "lineTo", "stroke", "pulseRadius", "pulseOpacity", "gradient", "createRadialGradient", "addColorStop", "arc", "indicatorY", "font", "textAlign", "stateText", "idle", "listening", "thinking", "speaking", "fillText", "<PERSON><PERSON><PERSON><PERSON>", "barSpacing", "numBars", "barsWidth", "startX", "barHeight", "style", "flex", "backgroundColor", "children", "ref"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/components/JoyDivisionVisualizer.jsx"], "sourcesContent": ["/**\n * JOY DIVISION STYLE AUDIO VISUALIZER\n * \"Love will tear us apart\" - but this visualization will bring us together\n * \n * Reactive concentric frequency lines that respond to voice input\n * and show AI agent states (listening, thinking, speaking)\n */\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport { View, Dimensions } from 'react-native';\n\nconst JoyDivisionVisualizer = ({ \n  isRecording = false, \n  isThinking = false, \n  isSpeaking = false,\n  isEmergency = false,\n  audioLevel = 0,\n  agentState = 'idle', // 'idle', 'listening', 'thinking', 'speaking', 'emergency'\n  emotion = 'neutral' // 'neutral', 'positive', 'negative', 'concerned'\n}) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const [dimensions, setDimensions] = useState(Dimensions.get('window'));\n\n  // Frequency data simulation\n  const frequencyData = useRef(new Array(64).fill(0));\n  const smoothedData = useRef(new Array(64).fill(0));\n  const time = useRef(0);\n\n  useEffect(() => {\n    const subscription = Dimensions.addEventListener('change', ({ window }) => {\n      setDimensions(window);\n    });\n    return () => subscription?.remove();\n  }, []);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    const { width, height } = dimensions;\n    \n    // Set canvas size\n    canvas.width = width;\n    canvas.height = height;\n\n    const centerX = width / 2;\n    const centerY = height / 2;\n    const maxRadius = Math.min(width, height) * 0.4;\n    const numRings = 12; // Number of concentric circles\n    const numPoints = 128; // Points per circle for smooth curves\n\n    const animate = () => {\n      time.current += 0.016; // ~60fps\n\n      // Clear canvas with dark background\n      ctx.fillStyle = '#0a0a0a';\n      ctx.fillRect(0, 0, width, height);\n\n      // Generate frequency data based on agent state\n      generateFrequencyData();\n\n      // Draw concentric frequency rings\n      drawFrequencyRings(ctx, centerX, centerY, maxRadius, numRings, numPoints);\n\n      // Draw center pulse\n      drawCenterPulse(ctx, centerX, centerY);\n\n      // Draw state indicator\n      drawStateIndicator(ctx, centerX, centerY, maxRadius);\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions, isRecording, isThinking, isSpeaking, audioLevel, agentState]);\n\n  const generateFrequencyData = () => {\n    const baseAmplitude = getBaseAmplitude();\n    const noiseLevel = getNoiseLevel();\n    \n    for (let i = 0; i < frequencyData.current.length; i++) {\n      let value = 0;\n      \n      // Generate different patterns based on agent state\n      switch (agentState) {\n        case 'listening':\n          // Reactive to audio input with high frequency emphasis\n          value = (audioLevel * 0.8 + Math.random() * 0.2) * \n                  (1 + Math.sin(time.current * 3 + i * 0.1) * 0.3) *\n                  (i / frequencyData.current.length); // Higher frequencies more active\n          break;\n          \n        case 'thinking':\n          // Pulsing waves with interference patterns\n          value = baseAmplitude * \n                  (0.5 + 0.5 * Math.sin(time.current * 2 + i * 0.2)) *\n                  (0.7 + 0.3 * Math.sin(time.current * 5 + i * 0.05)) *\n                  Math.exp(-Math.abs(i - 32) / 20); // Bell curve distribution\n          break;\n          \n        case 'speaking':\n          // Speech-like patterns with formants\n          const formant1 = Math.exp(-Math.pow(i - 8, 2) / 50);\n          const formant2 = Math.exp(-Math.pow(i - 24, 2) / 80);\n          const formant3 = Math.exp(-Math.pow(i - 45, 2) / 100);\n          value = baseAmplitude * 0.8 * \n                  (formant1 + formant2 * 0.7 + formant3 * 0.5) *\n                  (0.8 + 0.2 * Math.sin(time.current * 8 + i * 0.3));\n          break;\n          \n        default: // idle\n          // Gentle ambient noise\n          value = noiseLevel * 0.1 * \n                  (0.5 + 0.5 * Math.sin(time.current * 0.5 + i * 0.1));\n      }\n      \n      // Add some random noise for organic feel\n      value += (Math.random() - 0.5) * noiseLevel * 0.1;\n      \n      // Smooth the data for fluid animation\n      smoothedData.current[i] = smoothedData.current[i] * 0.85 + value * 0.15;\n      frequencyData.current[i] = Math.max(0, Math.min(1, smoothedData.current[i]));\n    }\n  };\n\n  const getBaseAmplitude = () => {\n    switch (agentState) {\n      case 'listening': return 0.6 + audioLevel * 0.4;\n      case 'thinking': return 0.7;\n      case 'speaking': return 0.8;\n      default: return 0.2;\n    }\n  };\n\n  const getNoiseLevel = () => {\n    switch (agentState) {\n      case 'listening': return 0.3;\n      case 'thinking': return 0.2;\n      case 'speaking': return 0.4;\n      default: return 0.1;\n    }\n  };\n\n  const getEmotionalColor = (ring) => {\n    const ringPhase = (ring / 12) * Math.PI * 2;\n    const timePhase = time.current * 0.5;\n\n    switch (agentState) {\n      case 'idle':\n        // Soft, calming blues and purples\n        return `rgba(${Math.floor(100 + 80 * Math.sin(timePhase + ringPhase))}, ${Math.floor(150 + 60 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase + 2))}, `;\n\n      case 'listening':\n        // Warm greens and teals\n        return `rgba(${Math.floor(80 + 60 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(180 + 75 * Math.sin(timePhase + ringPhase))}, ${Math.floor(140 + 70 * Math.sin(timePhase + ringPhase + 0.5))}, `;\n\n      case 'thinking':\n        // Gentle oranges and yellows\n        return `rgba(${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase))}, ${Math.floor(160 + 70 * Math.sin(timePhase + ringPhase + 0.8))}, ${Math.floor(100 + 50 * Math.sin(timePhase + ringPhase + 1.5))}, `;\n\n      case 'speaking':\n        // Soft pinks and lavenders\n        return `rgba(${Math.floor(180 + 60 * Math.sin(timePhase + ringPhase))}, ${Math.floor(130 + 70 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase + 0.5))}, `;\n\n      case 'emergency':\n        // Urgent but caring - warm reds with golden highlights\n        return `rgba(${Math.floor(220 + 35 * Math.sin(timePhase * 3 + ringPhase))}, ${Math.floor(100 + 80 * Math.sin(timePhase * 2 + ringPhase + 1))}, ${Math.floor(80 + 60 * Math.sin(timePhase + ringPhase + 2))}, `;\n\n      default:\n        return 'rgba(255, 255, 255, '; // Fallback white\n    }\n  };\n\n  const drawFrequencyRings = (ctx, centerX, centerY, maxRadius, numRings, numPoints) => {\n    const ringSpacing = maxRadius / numRings;\n    \n    for (let ring = 0; ring < numRings; ring++) {\n      const baseRadius = ringSpacing * (ring + 1);\n      const opacity = 1 - (ring / numRings) * 0.7; // Fade outer rings\n      \n      // Emotional color with transparency\n      const colorBase = getEmotionalColor(ring);\n      ctx.strokeStyle = `${colorBase}${opacity})`;\n      ctx.lineWidth = Math.max(1, 3 - ring * 0.2);\n      ctx.lineCap = 'round';\n      ctx.lineJoin = 'round';\n      \n      ctx.beginPath();\n\n      let firstX, firstY;\n\n      for (let i = 0; i <= numPoints; i++) {\n        const angle = (i / numPoints) * Math.PI * 2;\n\n        // Get frequency data for this angle\n        const freqIndex = Math.floor((i / numPoints) * frequencyData.current.length);\n        const amplitude = frequencyData.current[freqIndex] || 0;\n\n        // Add ring-specific modulation\n        const ringModulation = 1 + Math.sin(time.current * 1.5 + ring * 0.5) * 0.1;\n        const radiusVariation = amplitude * 30 * ringModulation;\n\n        const radius = baseRadius + radiusVariation;\n        const x = centerX + Math.cos(angle) * radius;\n        const y = centerY + Math.sin(angle) * radius;\n\n        if (i === 0) {\n          ctx.moveTo(x, y);\n          firstX = x;\n          firstY = y;\n        } else {\n          ctx.lineTo(x, y);\n        }\n      }\n\n      // Close properly without closePath to avoid center line\n      ctx.lineTo(firstX, firstY);\n      ctx.stroke();\n    }\n  };\n\n  const drawCenterPulse = (ctx, centerX, centerY) => {\n    const pulseRadius = 8 + getBaseAmplitude() * 12;\n    const pulseOpacity = 0.3 + getBaseAmplitude() * 0.4;\n    \n    // Outer glow\n    const gradient = ctx.createRadialGradient(\n      centerX, centerY, 0,\n      centerX, centerY, pulseRadius * 2\n    );\n    gradient.addColorStop(0, `rgba(255, 255, 255, ${pulseOpacity})`);\n    gradient.addColorStop(0.5, `rgba(255, 255, 255, ${pulseOpacity * 0.3})`);\n    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n    \n    ctx.fillStyle = gradient;\n    ctx.beginPath();\n    ctx.arc(centerX, centerY, pulseRadius * 2, 0, Math.PI * 2);\n    ctx.fill();\n    \n    // Center dot\n    ctx.fillStyle = `rgba(255, 255, 255, ${0.8 + pulseOpacity * 0.2})`;\n    ctx.beginPath();\n    ctx.arc(centerX, centerY, pulseRadius, 0, Math.PI * 2);\n    ctx.fill();\n  };\n\n  const drawStateIndicator = (ctx, centerX, centerY, maxRadius) => {\n    const indicatorY = centerY + maxRadius + 40;\n    \n    // State text\n    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';\n    ctx.font = '16px monospace';\n    ctx.textAlign = 'center';\n    \n    const stateText = {\n      idle: '◦ READY',\n      listening: '● LISTENING',\n      thinking: '◐ THINKING',\n      speaking: '◑ SPEAKING'\n    }[agentState] || '◦ READY';\n    \n    ctx.fillText(stateText, centerX, indicatorY);\n    \n    // Frequency bars below text\n    const barWidth = 2;\n    const barSpacing = 3;\n    const numBars = 32;\n    const barsWidth = numBars * (barWidth + barSpacing) - barSpacing;\n    const startX = centerX - barsWidth / 2;\n    \n    for (let i = 0; i < numBars; i++) {\n      const freqIndex = Math.floor((i / numBars) * frequencyData.current.length);\n      const amplitude = frequencyData.current[freqIndex] || 0;\n      const barHeight = amplitude * 20;\n      \n      const x = startX + i * (barWidth + barSpacing);\n      const y = indicatorY + 20;\n      \n      ctx.fillStyle = `rgba(255, 255, 255, ${0.3 + amplitude * 0.5})`;\n      ctx.fillRect(x, y - barHeight, barWidth, barHeight);\n    }\n  };\n\n  return (\n    <View style={{ flex: 1, backgroundColor: '#0a0a0a' }}>\n      <canvas\n        ref={canvasRef}\n        style={{\n          width: '100%',\n          height: '100%',\n          backgroundColor: '#0a0a0a'\n        }}\n      />\n    </View>\n  );\n};\n\nexport default JoyDivisionVisualizer;\n"], "mappings": ";AAQA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,SAAAC,GAAA,IAAAC,IAAA;AAG3D,IAAMC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAAC,IAAA,EAQrB;EAAA,IAAAC,gBAAA,GAAAD,IAAA,CAPJE,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,KAAK,GAAAA,gBAAA;IAAAE,eAAA,GAAAH,IAAA,CACnBI,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,KAAK,GAAAA,eAAA;IAAAE,eAAA,GAAAL,IAAA,CAClBM,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,KAAK,GAAAA,eAAA;IAAAE,gBAAA,GAAAP,IAAA,CAClBQ,WAAW;IAAXA,WAAW,GAAAD,gBAAA,cAAG,KAAK,GAAAA,gBAAA;IAAAE,eAAA,GAAAT,IAAA,CACnBU,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,CAAC,GAAAA,eAAA;IAAAE,eAAA,GAAAX,IAAA,CACdY,UAAU;IAAVA,UAAU,GAAAD,eAAA,cAAG,MAAM,GAAAA,eAAA;IAAAE,YAAA,GAAAb,IAAA,CACnBc,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,SAAS,GAAAA,YAAA;EAEnB,IAAME,SAAS,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAC9B,IAAMwB,YAAY,GAAGxB,MAAM,CAAC,IAAI,CAAC;EACjC,IAAAyB,SAAA,GAAoCvB,QAAQ,CAACE,UAAU,CAACsB,GAAG,CAAC,QAAQ,CAAC,CAAC;IAAAC,UAAA,GAAAC,cAAA,CAAAH,SAAA;IAA/DI,UAAU,GAAAF,UAAA;IAAEG,aAAa,GAAAH,UAAA;EAGhC,IAAMI,aAAa,GAAG/B,MAAM,CAAC,IAAIgC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EACnD,IAAMC,YAAY,GAAGlC,MAAM,CAAC,IAAIgC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EAClD,IAAME,IAAI,GAAGnC,MAAM,CAAC,CAAC,CAAC;EAEtBC,SAAS,CAAC,YAAM;IACd,IAAMmC,YAAY,GAAGhC,UAAU,CAACiC,gBAAgB,CAAC,QAAQ,EAAE,UAAAC,KAAA,EAAgB;MAAA,IAAbC,MAAM,GAAAD,KAAA,CAANC,MAAM;MAClET,aAAa,CAACS,MAAM,CAAC;IACvB,CAAC,CAAC;IACF,OAAO;MAAA,OAAMH,YAAY,oBAAZA,YAAY,CAAEI,MAAM,CAAC,CAAC;IAAA;EACrC,CAAC,EAAE,EAAE,CAAC;EAENvC,SAAS,CAAC,YAAM;IACd,IAAMwC,MAAM,GAAGlB,SAAS,CAACmB,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,IAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAQC,KAAK,GAAahB,UAAU,CAA5BgB,KAAK;MAAEC,MAAM,GAAKjB,UAAU,CAArBiB,MAAM;IAGrBL,MAAM,CAACI,KAAK,GAAGA,KAAK;IACpBJ,MAAM,CAACK,MAAM,GAAGA,MAAM;IAEtB,IAAMC,OAAO,GAAGF,KAAK,GAAG,CAAC;IACzB,IAAMG,OAAO,GAAGF,MAAM,GAAG,CAAC;IAC1B,IAAMG,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACN,KAAK,EAAEC,MAAM,CAAC,GAAG,GAAG;IAC/C,IAAMM,QAAQ,GAAG,EAAE;IACnB,IAAMC,SAAS,GAAG,GAAG;IAErB,IAAMC,QAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpBnB,IAAI,CAACO,OAAO,IAAI,KAAK;MAGrBC,GAAG,CAACY,SAAS,GAAG,SAAS;MACzBZ,GAAG,CAACa,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEX,KAAK,EAAEC,MAAM,CAAC;MAGjCW,qBAAqB,CAAC,CAAC;MAGvBC,kBAAkB,CAACf,GAAG,EAAEI,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAEG,QAAQ,EAAEC,SAAS,CAAC;MAGzEM,eAAe,CAAChB,GAAG,EAAEI,OAAO,EAAEC,OAAO,CAAC;MAGtCY,kBAAkB,CAACjB,GAAG,EAAEI,OAAO,EAAEC,OAAO,EAAEC,SAAS,CAAC;MAEpDzB,YAAY,CAACkB,OAAO,GAAGmB,qBAAqB,CAACP,QAAO,CAAC;IACvD,CAAC;IAEDA,QAAO,CAAC,CAAC;IAET,OAAO,YAAM;MACX,IAAI9B,YAAY,CAACkB,OAAO,EAAE;QACxBoB,oBAAoB,CAACtC,YAAY,CAACkB,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,CAACb,UAAU,EAAEnB,WAAW,EAAEE,UAAU,EAAEE,UAAU,EAAEI,UAAU,EAAEE,UAAU,CAAC,CAAC;EAE7E,IAAMqC,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;IAClC,IAAMM,aAAa,GAAGC,gBAAgB,CAAC,CAAC;IACxC,IAAMC,UAAU,GAAGC,aAAa,CAAC,CAAC;IAElC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,aAAa,CAACW,OAAO,CAAC0B,MAAM,EAAED,CAAC,EAAE,EAAE;MACrD,IAAIE,KAAK,GAAG,CAAC;MAGb,QAAQjD,UAAU;QAChB,KAAK,WAAW;UAEdiD,KAAK,GAAG,CAACnD,UAAU,GAAG,GAAG,GAAGgC,IAAI,CAACoB,MAAM,CAAC,CAAC,GAAG,GAAG,KACtC,CAAC,GAAGpB,IAAI,CAACqB,GAAG,CAACpC,IAAI,CAACO,OAAO,GAAG,CAAC,GAAGyB,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAC/CA,CAAC,GAAGpC,aAAa,CAACW,OAAO,CAAC0B,MAAM,CAAC;UAC1C;QAEF,KAAK,UAAU;UAEbC,KAAK,GAAGN,aAAa,IACZ,GAAG,GAAG,GAAG,GAAGb,IAAI,CAACqB,GAAG,CAACpC,IAAI,CAACO,OAAO,GAAG,CAAC,GAAGyB,CAAC,GAAG,GAAG,CAAC,CAAC,IACjD,GAAG,GAAG,GAAG,GAAGjB,IAAI,CAACqB,GAAG,CAACpC,IAAI,CAACO,OAAO,GAAG,CAAC,GAAGyB,CAAC,GAAG,IAAI,CAAC,CAAC,GACnDjB,IAAI,CAACsB,GAAG,CAAC,CAACtB,IAAI,CAACuB,GAAG,CAACN,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;UACxC;QAEF,KAAK,UAAU;UAEb,IAAMO,QAAQ,GAAGxB,IAAI,CAACsB,GAAG,CAAC,CAACtB,IAAI,CAACyB,GAAG,CAACR,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;UACnD,IAAMS,QAAQ,GAAG1B,IAAI,CAACsB,GAAG,CAAC,CAACtB,IAAI,CAACyB,GAAG,CAACR,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;UACpD,IAAMU,QAAQ,GAAG3B,IAAI,CAACsB,GAAG,CAAC,CAACtB,IAAI,CAACyB,GAAG,CAACR,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;UACrDE,KAAK,GAAGN,aAAa,GAAG,GAAG,IAClBW,QAAQ,GAAGE,QAAQ,GAAG,GAAG,GAAGC,QAAQ,GAAG,GAAG,CAAC,IAC3C,GAAG,GAAG,GAAG,GAAG3B,IAAI,CAACqB,GAAG,CAACpC,IAAI,CAACO,OAAO,GAAG,CAAC,GAAGyB,CAAC,GAAG,GAAG,CAAC,CAAC;UAC1D;QAEF;UAEEE,KAAK,GAAGJ,UAAU,GAAG,GAAG,IACf,GAAG,GAAG,GAAG,GAAGf,IAAI,CAACqB,GAAG,CAACpC,IAAI,CAACO,OAAO,GAAG,GAAG,GAAGyB,CAAC,GAAG,GAAG,CAAC,CAAC;MAChE;MAGAE,KAAK,IAAI,CAACnB,IAAI,CAACoB,MAAM,CAAC,CAAC,GAAG,GAAG,IAAIL,UAAU,GAAG,GAAG;MAGjD/B,YAAY,CAACQ,OAAO,CAACyB,CAAC,CAAC,GAAGjC,YAAY,CAACQ,OAAO,CAACyB,CAAC,CAAC,GAAG,IAAI,GAAGE,KAAK,GAAG,IAAI;MACvEtC,aAAa,CAACW,OAAO,CAACyB,CAAC,CAAC,GAAGjB,IAAI,CAAC4B,GAAG,CAAC,CAAC,EAAE5B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjB,YAAY,CAACQ,OAAO,CAACyB,CAAC,CAAC,CAAC,CAAC;IAC9E;EACF,CAAC;EAED,IAAMH,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7B,QAAQ5C,UAAU;MAChB,KAAK,WAAW;QAAE,OAAO,GAAG,GAAGF,UAAU,GAAG,GAAG;MAC/C,KAAK,UAAU;QAAE,OAAO,GAAG;MAC3B,KAAK,UAAU;QAAE,OAAO,GAAG;MAC3B;QAAS,OAAO,GAAG;IACrB;EACF,CAAC;EAED,IAAMgD,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;IAC1B,QAAQ9C,UAAU;MAChB,KAAK,WAAW;QAAE,OAAO,GAAG;MAC5B,KAAK,UAAU;QAAE,OAAO,GAAG;MAC3B,KAAK,UAAU;QAAE,OAAO,GAAG;MAC3B;QAAS,OAAO,GAAG;IACrB;EACF,CAAC;EAED,IAAM2D,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,IAAI,EAAK;IAClC,IAAMC,SAAS,GAAID,IAAI,GAAG,EAAE,GAAI9B,IAAI,CAACgC,EAAE,GAAG,CAAC;IAC3C,IAAMC,SAAS,GAAGhD,IAAI,CAACO,OAAO,GAAG,GAAG;IAEpC,QAAQtB,UAAU;MAChB,KAAK,MAAM;QAET,OAAO,QAAQ8B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI;MAEzM,KAAK,WAAW;QAEd,OAAO,QAAQ/B,IAAI,CAACkC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,GAAG,CAAC,CAAC,IAAI;MAE1M,KAAK,UAAU;QAEb,OAAO,QAAQ/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,GAAG,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,GAAG,CAAC,CAAC,IAAI;MAE7M,KAAK,UAAU;QAEb,OAAO,QAAQ/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,GAAG,CAAC,CAAC,IAAI;MAE3M,KAAK,WAAW;QAEd,OAAO,QAAQ/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAG,CAAC,GAAGF,SAAS,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,GAAG,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAG,CAAC,GAAGF,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK/B,IAAI,CAACkC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAGlC,IAAI,CAACqB,GAAG,CAACY,SAAS,GAAGF,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI;MAEhN;QACE,OAAO,sBAAsB;IACjC;EACF,CAAC;EAED,IAAMvB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIf,GAAG,EAAEI,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAEG,QAAQ,EAAEC,SAAS,EAAK;IACpF,IAAMgC,WAAW,GAAGpC,SAAS,GAAGG,QAAQ;IAExC,KAAK,IAAI4B,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG5B,QAAQ,EAAE4B,IAAI,EAAE,EAAE;MAC1C,IAAMM,UAAU,GAAGD,WAAW,IAAIL,IAAI,GAAG,CAAC,CAAC;MAC3C,IAAMO,OAAO,GAAG,CAAC,GAAIP,IAAI,GAAG5B,QAAQ,GAAI,GAAG;MAG3C,IAAMoC,SAAS,GAAGT,iBAAiB,CAACC,IAAI,CAAC;MACzCrC,GAAG,CAAC8C,WAAW,GAAG,GAAGD,SAAS,GAAGD,OAAO,GAAG;MAC3C5C,GAAG,CAAC+C,SAAS,GAAGxC,IAAI,CAAC4B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGE,IAAI,GAAG,GAAG,CAAC;MAC3CrC,GAAG,CAACgD,OAAO,GAAG,OAAO;MACrBhD,GAAG,CAACiD,QAAQ,GAAG,OAAO;MAEtBjD,GAAG,CAACkD,SAAS,CAAC,CAAC;MAEf,IAAIC,MAAM;QAAEC,MAAM;MAElB,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAId,SAAS,EAAEc,CAAC,EAAE,EAAE;QACnC,IAAM6B,KAAK,GAAI7B,CAAC,GAAGd,SAAS,GAAIH,IAAI,CAACgC,EAAE,GAAG,CAAC;QAG3C,IAAMe,SAAS,GAAG/C,IAAI,CAACkC,KAAK,CAAEjB,CAAC,GAAGd,SAAS,GAAItB,aAAa,CAACW,OAAO,CAAC0B,MAAM,CAAC;QAC5E,IAAM8B,SAAS,GAAGnE,aAAa,CAACW,OAAO,CAACuD,SAAS,CAAC,IAAI,CAAC;QAGvD,IAAME,cAAc,GAAG,CAAC,GAAGjD,IAAI,CAACqB,GAAG,CAACpC,IAAI,CAACO,OAAO,GAAG,GAAG,GAAGsC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;QAC1E,IAAMoB,eAAe,GAAGF,SAAS,GAAG,EAAE,GAAGC,cAAc;QAEvD,IAAME,MAAM,GAAGf,UAAU,GAAGc,eAAe;QAC3C,IAAME,CAAC,GAAGvD,OAAO,GAAGG,IAAI,CAACqD,GAAG,CAACP,KAAK,CAAC,GAAGK,MAAM;QAC5C,IAAMG,CAAC,GAAGxD,OAAO,GAAGE,IAAI,CAACqB,GAAG,CAACyB,KAAK,CAAC,GAAGK,MAAM;QAE5C,IAAIlC,CAAC,KAAK,CAAC,EAAE;UACXxB,GAAG,CAAC8D,MAAM,CAACH,CAAC,EAAEE,CAAC,CAAC;UAChBV,MAAM,GAAGQ,CAAC;UACVP,MAAM,GAAGS,CAAC;QACZ,CAAC,MAAM;UACL7D,GAAG,CAAC+D,MAAM,CAACJ,CAAC,EAAEE,CAAC,CAAC;QAClB;MACF;MAGA7D,GAAG,CAAC+D,MAAM,CAACZ,MAAM,EAAEC,MAAM,CAAC;MAC1BpD,GAAG,CAACgE,MAAM,CAAC,CAAC;IACd;EACF,CAAC;EAED,IAAMhD,eAAe,GAAG,SAAlBA,eAAeA,CAAIhB,GAAG,EAAEI,OAAO,EAAEC,OAAO,EAAK;IACjD,IAAM4D,WAAW,GAAG,CAAC,GAAG5C,gBAAgB,CAAC,CAAC,GAAG,EAAE;IAC/C,IAAM6C,YAAY,GAAG,GAAG,GAAG7C,gBAAgB,CAAC,CAAC,GAAG,GAAG;IAGnD,IAAM8C,QAAQ,GAAGnE,GAAG,CAACoE,oBAAoB,CACvChE,OAAO,EAAEC,OAAO,EAAE,CAAC,EACnBD,OAAO,EAAEC,OAAO,EAAE4D,WAAW,GAAG,CAClC,CAAC;IACDE,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,uBAAuBH,YAAY,GAAG,CAAC;IAChEC,QAAQ,CAACE,YAAY,CAAC,GAAG,EAAE,uBAAuBH,YAAY,GAAG,GAAG,GAAG,CAAC;IACxEC,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,wBAAwB,CAAC;IAElDrE,GAAG,CAACY,SAAS,GAAGuD,QAAQ;IACxBnE,GAAG,CAACkD,SAAS,CAAC,CAAC;IACflD,GAAG,CAACsE,GAAG,CAAClE,OAAO,EAAEC,OAAO,EAAE4D,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE1D,IAAI,CAACgC,EAAE,GAAG,CAAC,CAAC;IAC1DvC,GAAG,CAACV,IAAI,CAAC,CAAC;IAGVU,GAAG,CAACY,SAAS,GAAG,uBAAuB,GAAG,GAAGsD,YAAY,GAAG,GAAG,GAAG;IAClElE,GAAG,CAACkD,SAAS,CAAC,CAAC;IACflD,GAAG,CAACsE,GAAG,CAAClE,OAAO,EAAEC,OAAO,EAAE4D,WAAW,EAAE,CAAC,EAAE1D,IAAI,CAACgC,EAAE,GAAG,CAAC,CAAC;IACtDvC,GAAG,CAACV,IAAI,CAAC,CAAC;EACZ,CAAC;EAED,IAAM2B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIjB,GAAG,EAAEI,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAK;IAC/D,IAAMiE,UAAU,GAAGlE,OAAO,GAAGC,SAAS,GAAG,EAAE;IAG3CN,GAAG,CAACY,SAAS,GAAG,0BAA0B;IAC1CZ,GAAG,CAACwE,IAAI,GAAG,gBAAgB;IAC3BxE,GAAG,CAACyE,SAAS,GAAG,QAAQ;IAExB,IAAMC,SAAS,GAAG;MAChBC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,aAAa;MACxBC,QAAQ,EAAE,YAAY;MACtBC,QAAQ,EAAE;IACZ,CAAC,CAACrG,UAAU,CAAC,IAAI,SAAS;IAE1BuB,GAAG,CAAC+E,QAAQ,CAACL,SAAS,EAAEtE,OAAO,EAAEmE,UAAU,CAAC;IAG5C,IAAMS,QAAQ,GAAG,CAAC;IAClB,IAAMC,UAAU,GAAG,CAAC;IACpB,IAAMC,OAAO,GAAG,EAAE;IAClB,IAAMC,SAAS,GAAGD,OAAO,IAAIF,QAAQ,GAAGC,UAAU,CAAC,GAAGA,UAAU;IAChE,IAAMG,MAAM,GAAGhF,OAAO,GAAG+E,SAAS,GAAG,CAAC;IAEtC,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,OAAO,EAAE1D,CAAC,EAAE,EAAE;MAChC,IAAM8B,SAAS,GAAG/C,IAAI,CAACkC,KAAK,CAAEjB,CAAC,GAAG0D,OAAO,GAAI9F,aAAa,CAACW,OAAO,CAAC0B,MAAM,CAAC;MAC1E,IAAM8B,SAAS,GAAGnE,aAAa,CAACW,OAAO,CAACuD,SAAS,CAAC,IAAI,CAAC;MACvD,IAAM+B,SAAS,GAAG9B,SAAS,GAAG,EAAE;MAEhC,IAAMI,CAAC,GAAGyB,MAAM,GAAG5D,CAAC,IAAIwD,QAAQ,GAAGC,UAAU,CAAC;MAC9C,IAAMpB,CAAC,GAAGU,UAAU,GAAG,EAAE;MAEzBvE,GAAG,CAACY,SAAS,GAAG,uBAAuB,GAAG,GAAG2C,SAAS,GAAG,GAAG,GAAG;MAC/DvD,GAAG,CAACa,QAAQ,CAAC8C,CAAC,EAAEE,CAAC,GAAGwB,SAAS,EAAEL,QAAQ,EAAEK,SAAS,CAAC;IACrD;EACF,CAAC;EAED,OACE1H,IAAA,CAACH,IAAI;IAAC8H,KAAK,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,eAAe,EAAE;IAAU,CAAE;IAAAC,QAAA,EACnD9H,IAAA;MACE+H,GAAG,EAAE9G,SAAU;MACf0G,KAAK,EAAE;QACLpF,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdqF,eAAe,EAAE;MACnB;IAAE,CACH;EAAC,CACE,CAAC;AAEX,CAAC;AAED,eAAe5H,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}