{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport KeyboardAvoidingView from \"react-native-web/dist/exports/KeyboardAvoidingView\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { Ionicons } from '@expo/vector-icons';\nimport { Audio } from 'expo-av';\nimport * as ImagePicker from 'expo-image-picker';\nimport io from 'socket.io-client';\nimport JoyDivisionVisualizer from \"../components/JoyDivisionVisualizer\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar SERVER_URL = 'http://localhost:3000';\nvar Agent = function Agent() {\n  var _useState = useState('idle'),\n    _useState2 = _slicedToArray(_useState, 2),\n    blobState = _useState2[0],\n    setBlobState = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    recording = _useState4[0],\n    setRecording = _useState4[1];\n  var _useState5 = useState(''),\n    _useState6 = _slicedToArray(_useState5, 2),\n    textInput = _useState6[0],\n    setTextInput = _useState6[1];\n  var _useState7 = useState(null),\n    _useState8 = _slicedToArray(_useState7, 2),\n    socket = _useState8[0],\n    setSocket = _useState8[1];\n  var _useState9 = useState(0),\n    _useState0 = _slicedToArray(_useState9, 2),\n    audioLevel = _useState0[0],\n    setAudioLevel = _useState0[1];\n  var _useState1 = useState('idle'),\n    _useState10 = _slicedToArray(_useState1, 2),\n    agentState = _useState10[0],\n    setAgentState = _useState10[1];\n  var _useState11 = useState('neutral'),\n    _useState12 = _slicedToArray(_useState11, 2),\n    emotion = _useState12[0],\n    setEmotion = _useState12[1];\n  var _useState13 = useState(null),\n    _useState14 = _slicedToArray(_useState13, 2),\n    audioRecordingStatus = _useState14[0],\n    setAudioRecordingStatus = _useState14[1];\n  useEffect(function () {\n    var newSocket = io(SERVER_URL);\n    setSocket(newSocket);\n    newSocket.on('connect', function () {\n      console.log('Connected to server:', newSocket.id);\n    });\n    newSocket.on('disconnect', function () {\n      console.log('Disconnected from server');\n    });\n    newSocket.on('reconnect', function () {\n      console.log('Reconnected to server');\n    });\n    newSocket.on('newMessage', function (data) {\n      console.log('New message received:', data);\n      if (data.message) {\n        if (data.message.from === 'doctor') {\n          setAgentState('idle');\n          var doctorMessage = data.message.payload.text;\n          setTimeout(function () {\n            Alert.alert('👩‍⚕️ Doctor Message', doctorMessage, [{\n              text: 'OK',\n              style: 'default'\n            }]);\n          }, 500);\n        } else if (data.message.from === 'ai_companion') {\n          var aiText = data.message.payload.text || '';\n          if (/emergency|urgent|critical|immediate|severe/i.test(aiText)) {\n            setAgentState('emergency');\n            setEmotion('concerned');\n          } else if (/happy|great|excellent|good news|improving/i.test(aiText)) {\n            setEmotion('positive');\n          } else if (/concerned|worry|attention|careful/i.test(aiText)) {\n            setEmotion('concerned');\n          } else if (/bad|worse|declined|negative|problem/i.test(aiText)) {\n            setEmotion('negative');\n          } else {\n            setEmotion('neutral');\n          }\n        } else if (data.message.from === 'system') {\n          console.log('System message:', data.message.payload);\n        }\n      }\n      if (data.temporalLayer) {\n        console.log('Temporal layer updated:', data.temporalLayer);\n      }\n    });\n    newSocket.on('audioResponse', function () {\n      var _ref = _asyncToGenerator(function* (data) {\n        console.log('Audio response received:', data);\n        if (data.audioUrl) {\n          try {\n            setAgentState('speaking');\n            var _yield$Audio$Sound$cr = yield Audio.Sound.createAsync({\n                uri: `${SERVER_URL}${data.audioUrl}`\n              }, {\n                shouldPlay: true\n              }),\n              sound = _yield$Audio$Sound$cr.sound;\n            sound.setOnPlaybackStatusUpdate(function (status) {\n              if (status.isLoaded && !status.isBuffering && status.didJustFinish) {\n                setAgentState('idle');\n                sound.unloadAsync();\n              }\n            });\n          } catch (error) {\n            console.error('Error playing audio response:', error);\n            setAgentState('idle');\n          }\n        }\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    newSocket.on('safetyAlert', function (data) {\n      console.log('Safety alert received:', data);\n      if (data.level === 'red' || data.level === 'orange') {\n        setAgentState('emergency');\n        setEmotion('concerned');\n        Alert.alert('⚠️ Medical Alert', data.message || 'Important medical alert', [{\n          text: 'Acknowledge',\n          style: 'destructive'\n        }]);\n      }\n    });\n    return function () {\n      return newSocket.close();\n    };\n  }, [blobState]);\n  var startRecording = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      try {\n        var permission = yield Audio.requestPermissionsAsync();\n        if (permission.status !== 'granted') {\n          Alert.alert('Permission required', 'Please grant microphone permission');\n          return;\n        }\n        yield Audio.setAudioModeAsync({\n          allowsRecordingIOS: true,\n          playsInSilentModeIOS: true\n        });\n        setBlobState('recording');\n        setAgentState('listening');\n        var _yield$Audio$Recordin = yield Audio.Recording.createAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY, function (status) {\n            setAudioRecordingStatus(status);\n            if (status.metering && status.metering > -50) {\n              var level = Math.max(0, (status.metering + 50) / 5);\n              setAudioLevel(level);\n            }\n          }, 100),\n          _recording = _yield$Audio$Recordin.recording;\n        setRecording(_recording);\n        setTimeout(function () {\n          if (_recording) {\n            stopRecording();\n          }\n        }, 5000);\n      } catch (err) {\n        console.error('Failed to start recording', err);\n        setBlobState('idle');\n        setAgentState('idle');\n      }\n    });\n    return function startRecording() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var stopRecording = function () {\n    var _ref3 = _asyncToGenerator(function* () {\n      if (!recording) return;\n      setBlobState('thinking');\n      setAgentState('thinking');\n      setAudioLevel(0);\n      setRecording(null);\n      try {\n        yield recording.stopAndUnloadAsync();\n        var uri = recording.getURI();\n        if (uri) {\n          yield uploadAudio(uri);\n        }\n      } catch (error) {\n        console.error('Failed to stop recording', error);\n        setBlobState('idle');\n        setAgentState('idle');\n      }\n    });\n    return function stopRecording() {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var uploadAudio = function () {\n    var _ref4 = _asyncToGenerator(function* (uri) {\n      try {\n        var formData = new FormData();\n        formData.append('audio', {\n          uri: uri,\n          type: 'audio/mp4',\n          name: 'recording.mp4'\n        });\n        var sttResponse = yield fetch(`${SERVER_URL}/watson/stt`, {\n          method: 'POST',\n          body: formData,\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        var transcription = '';\n        if (sttResponse.ok) {\n          var sttResult = yield sttResponse.json();\n          transcription = sttResult.transcription || 'Unable to transcribe audio';\n          console.log('Transcription result:', sttResult);\n        } else {\n          var mockTranscriptions = [\"I have been experiencing severe headaches for the past three days\", \"My stomach has been hurting and I feel nauseous\", \"I'm having trouble sleeping and feel very anxious\", \"The medication you prescribed isn't helping much\", \"I feel dizzy when I stand up quickly\"];\n          transcription = mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];\n          console.log('Using mock transcription due to STT failure');\n        }\n        var response = yield fetch(`${SERVER_URL}/chat/patient/default-patient`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            message: transcription\n          })\n        });\n        if (response.ok) {\n          var result = yield response.json();\n          console.log('Voice message processed:', result);\n          var aiResponseText = result.aiResponse.payload.text;\n          try {\n            var ttsResponse = yield fetch(`${SERVER_URL}/watson/tts`, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                text: aiResponseText,\n                voice: 'en-US_AllisonV3Voice',\n                patientId: 'default-patient'\n              })\n            });\n            if (ttsResponse.ok) {\n              var ttsResult = yield ttsResponse.json();\n              console.log('TTS audio generated:', ttsResult.audioUrl);\n              try {\n                var _yield$Audio$Sound$cr2 = yield Audio.Sound.createAsync({\n                    uri: `${SERVER_URL}${ttsResult.audioUrl}`\n                  }, {\n                    shouldPlay: true\n                  }),\n                  sound = _yield$Audio$Sound$cr2.sound;\n                setAgentState('speaking');\n                sound.setOnPlaybackStatusUpdate(function (status) {\n                  if (status.isLoaded && !status.isBuffering && status.didJustFinish) {\n                    setAgentState('idle');\n                    setEmotion('neutral');\n                    sound.unloadAsync();\n                  }\n                });\n                Alert.alert('🎤 Voice Message Processed', `You said: \"${transcription}\"`, [{\n                  text: 'OK',\n                  style: 'default'\n                }]);\n              } catch (playError) {\n                console.error('Audio playback error:', playError);\n                Alert.alert('🎤 Voice Message Processed', `You said: \"${transcription}\"\\n\\n🤖 AI Response: ${aiResponseText}\\n\\nCouldn't play audio response.`, [{\n                  text: 'OK',\n                  style: 'default'\n                }]);\n              }\n            } else {\n              setTimeout(function () {\n                Alert.alert('🎤 Voice Message Processed', `You said: \"${transcription}\"\\n\\n🤖 AI Response: ${aiResponseText}`, [{\n                  text: 'OK',\n                  style: 'default'\n                }]);\n              }, 1000);\n            }\n          } catch (ttsError) {\n            console.error('TTS error:', ttsError);\n            setTimeout(function () {\n              Alert.alert('🎤 Voice Message Processed', `You said: \"${transcription}\"\\n\\n🤖 AI Response: ${aiResponseText}`, [{\n                text: 'OK',\n                style: 'default'\n              }]);\n            }, 1000);\n          }\n        } else {\n          throw new Error('Upload failed');\n        }\n      } catch (error) {\n        console.error('Audio upload error:', error);\n        Alert.alert('Error', 'Failed to process voice message');\n      } finally {\n        setBlobState('idle');\n      }\n    });\n    return function uploadAudio(_x2) {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var pickImage = function () {\n    var _ref5 = _asyncToGenerator(function* () {\n      var result = yield ImagePicker.launchImageLibraryAsync({\n        mediaTypes: ImagePicker.MediaTypeOptions.Images,\n        allowsEditing: true,\n        aspect: [4, 3],\n        quality: 1\n      });\n      if (!result.canceled && result.assets[0]) {\n        setBlobState('thinking');\n        yield uploadImage(result.assets[0].uri);\n      }\n    });\n    return function pickImage() {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  var uploadImage = function () {\n    var _ref6 = _asyncToGenerator(function* (uri) {\n      try {\n        var formData = new FormData();\n        formData.append('file', {\n          uri: uri,\n          type: 'image/jpeg',\n          name: 'image.jpg'\n        });\n        var response = yield fetch(`${SERVER_URL}/upload`, {\n          method: 'POST',\n          body: formData,\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (response.ok) {\n          var result = yield response.json();\n          console.log('Image uploaded successfully:', result);\n        } else {\n          throw new Error('Upload failed');\n        }\n      } catch (error) {\n        console.error('Image upload error:', error);\n        Alert.alert('Error', 'Failed to upload image');\n      } finally {\n        setBlobState('idle');\n      }\n    });\n    return function uploadImage(_x3) {\n      return _ref6.apply(this, arguments);\n    };\n  }();\n  var sendTextMessage = function () {\n    var _ref7 = _asyncToGenerator(function* () {\n      if (!textInput.trim()) return;\n      setBlobState('thinking');\n      try {\n        var response = yield fetch(`${SERVER_URL}/chat/patient/default-patient`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            message: textInput.trim()\n          })\n        });\n        if (response.ok) {\n          var result = yield response.json();\n          console.log('AI Response received:', result.aiResponse.payload.text);\n          setTextInput('');\n          setTimeout(function () {\n            Alert.alert('🤖 AI Companion Response', result.aiResponse.payload.text, [{\n              text: 'OK',\n              style: 'default'\n            }]);\n          }, 1000);\n        } else {\n          throw new Error('Send failed');\n        }\n      } catch (error) {\n        console.error('Text message error:', error);\n        Alert.alert('Error', 'Failed to send message');\n      } finally {\n        setBlobState('idle');\n      }\n    });\n    return function sendTextMessage() {\n      return _ref7.apply(this, arguments);\n    };\n  }();\n  return _jsxs(KeyboardAvoidingView, {\n    style: styles.container,\n    behavior: Platform.OS === 'ios' ? 'padding' : 'height',\n    children: [_jsxs(View, {\n      style: styles.header,\n      children: [_jsxs(View, {\n        style: styles.logoContainer,\n        children: [_jsx(Text, {\n          style: styles.logoIcon,\n          children: \"\\uD83E\\uDDE0\"\n        }), _jsx(Text, {\n          style: styles.title,\n          children: \"Lyxara\"\n        })]\n      }), _jsx(Text, {\n        style: styles.subtitle,\n        children: \"Your AI Health Companion\"\n      })]\n    }), _jsxs(View, {\n      style: styles.blobContainer,\n      children: [_jsx(JoyDivisionVisualizer, {\n        agentState: agentState,\n        audioLevel: audioLevel,\n        isRecording: blobState === 'recording',\n        isThinking: blobState === 'thinking',\n        isSpeaking: agentState === 'speaking',\n        isEmergency: agentState === 'emergency',\n        emotion: emotion\n      }), _jsxs(Text, {\n        style: styles.stateText,\n        children: [agentState === 'idle' && 'Tap to share how you\\'re feeling', agentState === 'listening' && 'Listening... speak freely', agentState === 'thinking' && 'Lyxara is analyzing...', agentState === 'speaking' && 'Lyxara is responding...', agentState === 'emergency' && '🚨 Urgent medical attention needed!']\n      })]\n    }), _jsxs(View, {\n      style: styles.controls,\n      children: [_jsx(TouchableOpacity, {\n        style: [styles.micButton, blobState === 'recording' && styles.recordingButton],\n        onPress: blobState === 'recording' ? stopRecording : startRecording,\n        disabled: blobState === 'thinking',\n        children: _jsx(Ionicons, {\n          name: blobState === 'recording' ? 'stop' : 'mic',\n          size: 32,\n          color: \"white\"\n        })\n      }), _jsx(TouchableOpacity, {\n        style: styles.imageButton,\n        onPress: pickImage,\n        disabled: blobState !== 'idle',\n        children: _jsx(Ionicons, {\n          name: \"camera\",\n          size: 24,\n          color: \"#60A5FA\"\n        })\n      })]\n    }), _jsxs(View, {\n      style: styles.textInputContainer,\n      children: [_jsx(TextInput, {\n        style: styles.textInput,\n        placeholder: \"Chat with Lyxara about your health...\",\n        placeholderTextColor: \"#9CA3AF\",\n        value: textInput,\n        onChangeText: setTextInput,\n        multiline: true\n      }), _jsx(TouchableOpacity, {\n        style: [styles.sendButton, !textInput.trim() && styles.sendButtonDisabled],\n        onPress: sendTextMessage,\n        disabled: !textInput.trim(),\n        children: _jsx(Ionicons, {\n          name: \"send\",\n          size: 20,\n          color: textInput.trim() ? '#007AFF' : '#C7C7CC'\n        })\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#111827',\n    paddingTop: 60\n  },\n  header: {\n    alignItems: 'center',\n    paddingHorizontal: 20,\n    marginBottom: 40,\n    paddingVertical: 20,\n    backgroundColor: 'rgba(31, 41, 55, 0.9)',\n    borderRadius: 20,\n    marginHorizontal: 20,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 4\n    },\n    shadowOpacity: 0.3,\n    shadowRadius: 12,\n    elevation: 8,\n    borderWidth: 1,\n    borderColor: '#374151'\n  },\n  logoContainer: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginBottom: 8\n  },\n  logoIcon: {\n    fontSize: 32,\n    marginRight: 8\n  },\n  title: {\n    fontSize: 32,\n    fontWeight: '800',\n    color: '#60A5FA',\n    textAlign: 'center'\n  },\n  subtitle: {\n    fontSize: 17,\n    color: '#9CA3AF',\n    textAlign: 'center',\n    fontWeight: '500'\n  },\n  blobContainer: {\n    flex: 1,\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingHorizontal: 20\n  },\n  stateText: {\n    fontSize: 16,\n    color: '#D1D5DB',\n    textAlign: 'center',\n    marginTop: 20\n  },\n  controls: {\n    flexDirection: 'row',\n    justifyContent: 'center',\n    alignItems: 'center',\n    paddingHorizontal: 20,\n    marginBottom: 20,\n    gap: 20\n  },\n  micButton: {\n    width: 90,\n    height: 90,\n    borderRadius: 45,\n    backgroundColor: '#3B82F6',\n    alignItems: 'center',\n    justifyContent: 'center',\n    shadowColor: '#3B82F6',\n    shadowOffset: {\n      width: 0,\n      height: 8\n    },\n    shadowOpacity: 0.3,\n    shadowRadius: 16,\n    elevation: 12\n  },\n  recordingButton: {\n    backgroundColor: '#EF4444',\n    shadowColor: '#EF4444'\n  },\n  imageButton: {\n    width: 50,\n    height: 50,\n    borderRadius: 25,\n    backgroundColor: '#374151',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderWidth: 1,\n    borderColor: '#4B5563'\n  },\n  textInputContainer: {\n    flexDirection: 'row',\n    alignItems: 'flex-end',\n    paddingHorizontal: 20,\n    paddingBottom: 20,\n    gap: 10\n  },\n  textInput: {\n    flex: 1,\n    borderWidth: 1,\n    borderColor: '#4B5563',\n    borderRadius: 20,\n    paddingHorizontal: 16,\n    paddingVertical: 12,\n    backgroundColor: '#374151',\n    color: '#F9FAFB',\n    maxHeight: 100,\n    fontSize: 16\n  },\n  sendButton: {\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  sendButtonDisabled: {\n    opacity: 0.5\n  }\n});\nexport default Agent;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "TouchableOpacity", "StyleSheet", "<PERSON><PERSON>", "TextInput", "KeyboardAvoidingView", "Platform", "Ionicons", "Audio", "ImagePicker", "io", "JoyDivisionVisualizer", "jsx", "_jsx", "jsxs", "_jsxs", "SERVER_URL", "Agent", "_useState", "_useState2", "_slicedToArray", "blobState", "setBlobState", "_useState3", "_useState4", "recording", "setRecording", "_useState5", "_useState6", "textInput", "setTextInput", "_useState7", "_useState8", "socket", "setSocket", "_useState9", "_useState0", "audioLevel", "setAudioLevel", "_useState1", "_useState10", "agentState", "setAgentState", "_useState11", "_useState12", "emotion", "setEmotion", "_useState13", "_useState14", "audioRecordingStatus", "setAudioRecordingStatus", "newSocket", "on", "console", "log", "id", "data", "message", "from", "doctor<PERSON><PERSON><PERSON>", "payload", "text", "setTimeout", "alert", "style", "aiText", "test", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "_asyncToGenerator", "audioUrl", "_yield$Audio$Sound$cr", "Sound", "createAsync", "uri", "shouldPlay", "sound", "setOnPlaybackStatusUpdate", "status", "isLoaded", "isBuffering", "did<PERSON>ust<PERSON><PERSON>sh", "unloadAsync", "error", "_x", "apply", "arguments", "level", "close", "startRecording", "_ref2", "permission", "requestPermissionsAsync", "setAudioModeAsync", "allowsRecordingIOS", "playsInSilentModeIOS", "_yield$Audio$Recordin", "Recording", "RecordingOptionsPresets", "HIGH_QUALITY", "metering", "Math", "max", "stopRecording", "err", "_ref3", "stopAndUnloadAsync", "getURI", "uploadAudio", "_ref4", "formData", "FormData", "append", "type", "name", "sttResponse", "fetch", "method", "body", "headers", "transcription", "ok", "sttResult", "json", "mockTranscriptions", "floor", "random", "length", "response", "JSON", "stringify", "result", "aiResponseText", "aiResponse", "ttsResponse", "voice", "patientId", "ttsResult", "_yield$Audio$Sound$cr2", "playError", "ttsError", "Error", "_x2", "pickImage", "_ref5", "launchImageLibraryAsync", "mediaTypes", "MediaTypeOptions", "Images", "allowsEditing", "aspect", "quality", "canceled", "assets", "uploadImage", "_ref6", "_x3", "sendTextMessage", "_ref7", "trim", "styles", "container", "behavior", "OS", "children", "header", "logoContainer", "logoIcon", "title", "subtitle", "blobC<PERSON>r", "isRecording", "isThinking", "isSpeaking", "isEmergency", "stateText", "controls", "mi<PERSON><PERSON><PERSON><PERSON>", "recordingButton", "onPress", "disabled", "size", "color", "imageButton", "textInputContainer", "placeholder", "placeholderTextColor", "value", "onChangeText", "multiline", "sendButton", "sendButtonDisabled", "create", "flex", "backgroundColor", "paddingTop", "alignItems", "paddingHorizontal", "marginBottom", "paddingVertical", "borderRadius", "marginHorizontal", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "borderWidth", "borderColor", "flexDirection", "justifyContent", "fontSize", "marginRight", "fontWeight", "textAlign", "marginTop", "gap", "paddingBottom", "maxHeight", "opacity"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/screens/Agent.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  View,\n  Text,\n  TouchableOpacity,\n  StyleSheet,\n  Alert,\n  TextInput,\n  KeyboardAvoidingView,\n  Platform,\n} from 'react-native';\nimport { Ionicons } from '@expo/vector-icons';\nimport { Audio } from 'expo-av';\nimport * as ImagePicker from 'expo-image-picker';\nimport io from 'socket.io-client';\nimport ReactiveBlob from '../components/ReactiveBlob';\nimport JoyDivisionVisualizer from '../components/JoyDivisionVisualizer';\n\nconst SERVER_URL = 'http://localhost:3000';\n\ntype BlobState = 'idle' | 'recording' | 'thinking';\n\nconst Agent: React.FC = () => {\n  const [blobState, setBlobState] = useState<BlobState>('idle');\n  const [recording, setRecording] = useState<Audio.Recording | null>(null);\n  const [textInput, setTextInput] = useState('');\n  const [socket, setSocket] = useState<any>(null);\n  const [audioLevel, setAudioLevel] = useState(0);\n  const [agentState, setAgentState] = useState<'idle' | 'listening' | 'thinking' | 'speaking' | 'emergency'>('idle');\n  const [emotion, setEmotion] = useState<'neutral' | 'positive' | 'negative' | 'concerned'>('neutral');\n  const [audioRecordingStatus, setAudioRecordingStatus] = useState<Audio.RecordingStatus | null>(null);\n\n  useEffect(() => {\n    // Initialize socket connection\n    const newSocket = io(SERVER_URL);\n    setSocket(newSocket);\n\n    newSocket.on('connect', () => {\n      console.log('Connected to server:', newSocket.id);\n    });\n\n    newSocket.on('disconnect', () => {\n      console.log('Disconnected from server');\n    });\n\n    newSocket.on('reconnect', () => {\n      console.log('Reconnected to server');\n    });\n\n    newSocket.on('newMessage', (data: any) => {\n      console.log('New message received:', data);\n      \n      // Handle different message types\n      if (data.message) {\n        // Handle doctor messages\n        if (data.message.from === 'doctor') {\n          // Change agent state to show doctor is responding\n          setAgentState('idle'); // Reset any previous state\n          \n          // Display doctor messages\n          const doctorMessage = data.message.payload.text;\n          setTimeout(() => {\n            Alert.alert(\n              '👩‍⚕️ Doctor Message',\n              doctorMessage,\n              [{ text: 'OK', style: 'default' }]\n            );\n          }, 500);\n        }\n        \n        // Handle AI companion messages\n        else if (data.message.from === 'ai_companion') {\n          // If it's an AI response, analyze for emotional content\n          const aiText = data.message.payload.text || '';\n          \n          // Simple emotion detection based on keywords\n          if (/emergency|urgent|critical|immediate|severe/i.test(aiText)) {\n            setAgentState('emergency');\n            setEmotion('concerned');\n          } else if (/happy|great|excellent|good news|improving/i.test(aiText)) {\n            setEmotion('positive');\n          } else if (/concerned|worry|attention|careful/i.test(aiText)) {\n            setEmotion('concerned');\n          } else if (/bad|worse|declined|negative|problem/i.test(aiText)) {\n            setEmotion('negative');\n          } else {\n            // Default\n            setEmotion('neutral');\n          }\n        }\n        \n        // Handle system messages\n        else if (data.message.from === 'system') {\n          console.log('System message:', data.message.payload);\n        }\n      }\n      \n      // Handle temporal context updates\n      if (data.temporalLayer) {\n        console.log('Temporal layer updated:', data.temporalLayer);\n      }\n    });\n\n    newSocket.on('audioResponse', async (data: any) => {\n      console.log('Audio response received:', data);\n      \n      // Handle audio playback from doctor or system\n      if (data.audioUrl) {\n        try {\n          // Update agent state to speaking\n          setAgentState('speaking');\n          \n          // Play the audio\n          const { sound } = await Audio.Sound.createAsync(\n            { uri: `${SERVER_URL}${data.audioUrl}` },\n            { shouldPlay: true }\n          );\n          \n          // Listen for playback status updates\n          sound.setOnPlaybackStatusUpdate((status) => {\n            if (status.isLoaded && !status.isBuffering && status.didJustFinish) {\n              // Reset state when audio finishes playing\n              setAgentState('idle');\n              sound.unloadAsync();\n            }\n          });\n        } catch (error) {\n          console.error('Error playing audio response:', error);\n          setAgentState('idle');\n        }\n      }\n    });\n    \n    // Handle safety alerts (for emergency conditions)\n    newSocket.on('safetyAlert', (data: any) => {\n      console.log('Safety alert received:', data);\n      \n      if (data.level === 'red' || data.level === 'orange') {\n        setAgentState('emergency');\n        setEmotion('concerned');\n        \n        // Show critical alert\n        Alert.alert(\n          '⚠️ Medical Alert',\n          data.message || 'Important medical alert',\n          [{ text: 'Acknowledge', style: 'destructive' }]\n        );\n      }\n    });\n\n    return () => newSocket.close();\n  }, [blobState]);\n\n  const startRecording = async () => {\n    try {\n      const permission = await Audio.requestPermissionsAsync();\n      if (permission.status !== 'granted') {\n        Alert.alert('Permission required', 'Please grant microphone permission');\n        return;\n      }\n\n      await Audio.setAudioModeAsync({\n        allowsRecordingIOS: true,\n        playsInSilentModeIOS: true,\n      });\n\n      setBlobState('recording');\n      setAgentState('listening');\n      const { recording } = await Audio.Recording.createAsync(\n        Audio.RecordingOptionsPresets.HIGH_QUALITY,\n        (status) => {\n          setAudioRecordingStatus(status);\n          // Update audio level for visualizer based on metering\n          if (status.metering && status.metering > -50) {\n            // Convert dB metering to a 0-10 scale for visualization\n            const level = Math.max(0, (status.metering + 50) / 5);\n            setAudioLevel(level);\n          }\n        },\n        100 // Update every 100ms\n      );\n      setRecording(recording);\n\n      // Auto-stop after 5 seconds for demo\n      setTimeout(() => {\n        if (recording) {\n          stopRecording();\n        }\n      }, 5000);\n    } catch (err) {\n      console.error('Failed to start recording', err);\n      setBlobState('idle');\n      setAgentState('idle');\n    }\n  };\n\n  const stopRecording = async () => {\n    if (!recording) return;\n\n    setBlobState('thinking');\n    setAgentState('thinking');\n    setAudioLevel(0); // Reset audio level\n    setRecording(null);\n\n    try {\n      await recording.stopAndUnloadAsync();\n      const uri = recording.getURI();\n      \n      if (uri) {\n        await uploadAudio(uri);\n      }\n    } catch (error) {\n      console.error('Failed to stop recording', error);\n      setBlobState('idle');\n      setAgentState('idle');\n    }\n  };\n\n  const uploadAudio = async (uri: string) => {\n    try {\n      // First, upload audio to Watson Speech-to-Text service\n      const formData = new FormData();\n      formData.append('audio', {\n        uri,\n        type: 'audio/mp4',\n        name: 'recording.mp4',\n      } as any);\n\n      const sttResponse = await fetch(`${SERVER_URL}/watson/stt`, {\n        method: 'POST',\n        body: formData,\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      let transcription = '';\n      \n      if (sttResponse.ok) {\n        const sttResult = await sttResponse.json();\n        transcription = sttResult.transcription || 'Unable to transcribe audio';\n        console.log('Transcription result:', sttResult);\n      } else {\n        // Fallback to mock if Watson fails\n        const mockTranscriptions = [\n          \"I have been experiencing severe headaches for the past three days\",\n          \"My stomach has been hurting and I feel nauseous\",\n          \"I'm having trouble sleeping and feel very anxious\",\n          \"The medication you prescribed isn't helping much\",\n          \"I feel dizzy when I stand up quickly\"\n        ];\n        transcription = mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];\n        console.log('Using mock transcription due to STT failure');\n      }\n\n      // Send transcription to LLM chat\n      const response = await fetch(`${SERVER_URL}/chat/patient/default-patient`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: transcription,\n        }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('Voice message processed:', result);\n\n        // Generate speech for AI response using Watson TTS\n        const aiResponseText = result.aiResponse.payload.text;\n        \n        try {\n          const ttsResponse = await fetch(`${SERVER_URL}/watson/tts`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              text: aiResponseText,\n              voice: 'en-US_AllisonV3Voice',\n              patientId: 'default-patient'\n            }),\n          });\n\n          if (ttsResponse.ok) {\n            const ttsResult = await ttsResponse.json();\n            console.log('TTS audio generated:', ttsResult.audioUrl);\n            \n            // Play the audio response with Audio.Sound\n            try {\n              const { sound } = await Audio.Sound.createAsync(\n                { uri: `${SERVER_URL}${ttsResult.audioUrl}` },\n                { shouldPlay: true }\n              );\n              \n              // Update agent state to speaking while audio plays\n              setAgentState('speaking');\n              \n              // Listen for playback status updates\n              sound.setOnPlaybackStatusUpdate((status) => {\n                if (status.isLoaded && !status.isBuffering && status.didJustFinish) {\n                  // Reset state when audio finishes playing\n                  setAgentState('idle');\n                  setEmotion('neutral');\n                  sound.unloadAsync();\n                }\n              });\n              \n              // Show minimal notification that doesn't interrupt the audio experience\n              Alert.alert(\n                '🎤 Voice Message Processed',\n                `You said: \"${transcription}\"`,\n                [{ text: 'OK', style: 'default' }]\n              );\n            } catch (playError) {\n              console.error('Audio playback error:', playError);\n              Alert.alert(\n                '🎤 Voice Message Processed',\n                `You said: \"${transcription}\"\\n\\n🤖 AI Response: ${aiResponseText}\\n\\nCouldn't play audio response.`,\n                [{ text: 'OK', style: 'default' }]\n              );\n            }\n          } else {\n            // Fallback to text-only response\n            setTimeout(() => {\n              Alert.alert(\n                '🎤 Voice Message Processed',\n                `You said: \"${transcription}\"\\n\\n🤖 AI Response: ${aiResponseText}`,\n                [{ text: 'OK', style: 'default' }]\n              );\n            }, 1000);\n          }\n        } catch (ttsError) {\n          console.error('TTS error:', ttsError);\n          // Fallback to text-only response\n          setTimeout(() => {\n            Alert.alert(\n              '🎤 Voice Message Processed',\n              `You said: \"${transcription}\"\\n\\n🤖 AI Response: ${aiResponseText}`,\n              [{ text: 'OK', style: 'default' }]\n            );\n          }, 1000);\n        }\n      } else {\n        throw new Error('Upload failed');\n      }\n    } catch (error) {\n      console.error('Audio upload error:', error);\n      Alert.alert('Error', 'Failed to process voice message');\n    } finally {\n      setBlobState('idle');\n    }\n  };\n\n  const pickImage = async () => {\n    const result = await ImagePicker.launchImageLibraryAsync({\n      mediaTypes: ImagePicker.MediaTypeOptions.Images,\n      allowsEditing: true,\n      aspect: [4, 3],\n      quality: 1,\n    });\n\n    if (!result.canceled && result.assets[0]) {\n      setBlobState('thinking');\n      await uploadImage(result.assets[0].uri);\n    }\n  };\n\n  const uploadImage = async (uri: string) => {\n    try {\n      const formData = new FormData();\n      formData.append('file', {\n        uri,\n        type: 'image/jpeg',\n        name: 'image.jpg',\n      } as any);\n\n      const response = await fetch(`${SERVER_URL}/upload`, {\n        method: 'POST',\n        body: formData,\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('Image uploaded successfully:', result);\n      } else {\n        throw new Error('Upload failed');\n      }\n    } catch (error) {\n      console.error('Image upload error:', error);\n      Alert.alert('Error', 'Failed to upload image');\n    } finally {\n      setBlobState('idle');\n    }\n  };\n\n  const sendTextMessage = async () => {\n    if (!textInput.trim()) return;\n\n    setBlobState('thinking');\n\n    try {\n      // Use the new LLM chat endpoint\n      const response = await fetch(`${SERVER_URL}/chat/patient/default-patient`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: textInput.trim(),\n        }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('AI Response received:', result.aiResponse.payload.text);\n        setTextInput('');\n\n        // Show AI response in an alert for demo\n        setTimeout(() => {\n          Alert.alert(\n            '🤖 AI Companion Response',\n            result.aiResponse.payload.text,\n            [{ text: 'OK', style: 'default' }]\n          );\n        }, 1000);\n      } else {\n        throw new Error('Send failed');\n      }\n    } catch (error) {\n      console.error('Text message error:', error);\n      Alert.alert('Error', 'Failed to send message');\n    } finally {\n      setBlobState('idle');\n    }\n  };\n\n  return (\n    <KeyboardAvoidingView \n      style={styles.container} \n      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}\n    >\n      <View style={styles.header}>\n        <View style={styles.logoContainer}>\n          <Text style={styles.logoIcon}>🧠</Text>\n          <Text style={styles.title}>Lyxara</Text>\n        </View>\n        <Text style={styles.subtitle}>Your AI Health Companion</Text>\n      </View>\n      \n      <View style={styles.blobContainer}>\n        <JoyDivisionVisualizer\n          agentState={agentState}\n          audioLevel={audioLevel}\n          isRecording={blobState === 'recording'}\n          isThinking={blobState === 'thinking'}\n          isSpeaking={agentState === 'speaking'}\n          isEmergency={agentState === 'emergency'}\n          emotion={emotion}\n        />\n        \n        <Text style={styles.stateText}>\n          {agentState === 'idle' && 'Tap to share how you\\'re feeling'}\n          {agentState === 'listening' && 'Listening... speak freely'}\n          {agentState === 'thinking' && 'Lyxara is analyzing...'}\n          {agentState === 'speaking' && 'Lyxara is responding...'}\n          {agentState === 'emergency' && '🚨 Urgent medical attention needed!'}\n        </Text>\n      </View>\n\n      <View style={styles.controls}>\n        <TouchableOpacity\n          style={[styles.micButton, blobState === 'recording' && styles.recordingButton]}\n          onPress={blobState === 'recording' ? stopRecording : startRecording}\n          disabled={blobState === 'thinking'}\n        >\n          <Ionicons \n            name={blobState === 'recording' ? 'stop' : 'mic'} \n            size={32} \n            color=\"white\" \n          />\n        </TouchableOpacity>\n\n        <TouchableOpacity\n          style={styles.imageButton}\n          onPress={pickImage}\n          disabled={blobState !== 'idle'}\n        >\n          <Ionicons name=\"camera\" size={24} color=\"#60A5FA\" />\n        </TouchableOpacity>\n      </View>\n\n      <View style={styles.textInputContainer}>\n        <TextInput\n          style={styles.textInput}\n          placeholder=\"Chat with Lyxara about your health...\"\n          placeholderTextColor=\"#9CA3AF\"\n          value={textInput}\n          onChangeText={setTextInput}\n          multiline\n        />\n        <TouchableOpacity\n          style={[styles.sendButton, !textInput.trim() && styles.sendButtonDisabled]}\n          onPress={sendTextMessage}\n          disabled={!textInput.trim()}\n        >\n          <Ionicons name=\"send\" size={20} color={textInput.trim() ? '#007AFF' : '#C7C7CC'} />\n        </TouchableOpacity>\n      </View>\n    </KeyboardAvoidingView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#111827',\n    paddingTop: 60,\n  },\n  header: {\n    alignItems: 'center',\n    paddingHorizontal: 20,\n    marginBottom: 40,\n    paddingVertical: 20,\n    backgroundColor: 'rgba(31, 41, 55, 0.9)',\n    borderRadius: 20,\n    marginHorizontal: 20,\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 4 },\n    shadowOpacity: 0.3,\n    shadowRadius: 12,\n    elevation: 8,\n    borderWidth: 1,\n    borderColor: '#374151',\n  },\n  logoContainer: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginBottom: 8,\n  },\n  logoIcon: {\n    fontSize: 32,\n    marginRight: 8,\n  },\n  title: {\n    fontSize: 32,\n    fontWeight: '800',\n    color: '#60A5FA',\n    textAlign: 'center',\n  },\n  subtitle: {\n    fontSize: 17,\n    color: '#9CA3AF',\n    textAlign: 'center',\n    fontWeight: '500',\n  },\n  blobContainer: {\n    flex: 1,\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingHorizontal: 20,\n  },\n  stateText: {\n    fontSize: 16,\n    color: '#D1D5DB',\n    textAlign: 'center',\n    marginTop: 20,\n  },\n  controls: {\n    flexDirection: 'row',\n    justifyContent: 'center',\n    alignItems: 'center',\n    paddingHorizontal: 20,\n    marginBottom: 20,\n    gap: 20,\n  },\n  micButton: {\n    width: 90,\n    height: 90,\n    borderRadius: 45,\n    backgroundColor: '#3B82F6',\n    alignItems: 'center',\n    justifyContent: 'center',\n    shadowColor: '#3B82F6',\n    shadowOffset: { width: 0, height: 8 },\n    shadowOpacity: 0.3,\n    shadowRadius: 16,\n    elevation: 12,\n  },\n  recordingButton: {\n    backgroundColor: '#EF4444',\n    shadowColor: '#EF4444',\n  },\n  imageButton: {\n    width: 50,\n    height: 50,\n    borderRadius: 25,\n    backgroundColor: '#374151',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderWidth: 1,\n    borderColor: '#4B5563',\n  },\n  textInputContainer: {\n    flexDirection: 'row',\n    alignItems: 'flex-end',\n    paddingHorizontal: 20,\n    paddingBottom: 20,\n    gap: 10,\n  },\n  textInput: {\n    flex: 1,\n    borderWidth: 1,\n    borderColor: '#4B5563',\n    borderRadius: 20,\n    paddingHorizontal: 16,\n    paddingVertical: 12,\n    backgroundColor: '#374151',\n    color: '#F9FAFB',\n    maxHeight: 100,\n    fontSize: 16,\n  },\n  sendButton: {\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  sendButtonDisabled: {\n    opacity: 0.5,\n  },\n});\n\nexport default Agent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,oBAAA;AAAA,OAAAC,QAAA;AAWnD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAChD,OAAOC,EAAE,MAAM,kBAAkB;AAEjC,OAAOC,qBAAqB;AAA4C,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAExE,IAAMC,UAAU,GAAG,uBAAuB;AAI1C,IAAMC,KAAe,GAAG,SAAlBA,KAAeA,CAAA,EAAS;EAC5B,IAAAC,SAAA,GAAkCrB,QAAQ,CAAY,MAAM,CAAC;IAAAsB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAtDG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAkC1B,QAAQ,CAAyB,IAAI,CAAC;IAAA2B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAjEE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAAkC9B,QAAQ,CAAC,EAAE,CAAC;IAAA+B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAvCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4BlC,QAAQ,CAAM,IAAI,CAAC;IAAAmC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAAxCE,MAAM,GAAAD,UAAA;IAAEE,SAAS,GAAAF,UAAA;EACxB,IAAAG,UAAA,GAAoCtC,QAAQ,CAAC,CAAC,CAAC;IAAAuC,UAAA,GAAAhB,cAAA,CAAAe,UAAA;IAAxCE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAAoC1C,QAAQ,CAA+D,MAAM,CAAC;IAAA2C,WAAA,GAAApB,cAAA,CAAAmB,UAAA;IAA3GE,UAAU,GAAAD,WAAA;IAAEE,aAAa,GAAAF,WAAA;EAChC,IAAAG,WAAA,GAA8B9C,QAAQ,CAAoD,SAAS,CAAC;IAAA+C,WAAA,GAAAxB,cAAA,CAAAuB,WAAA;IAA7FE,OAAO,GAAAD,WAAA;IAAEE,UAAU,GAAAF,WAAA;EAC1B,IAAAG,WAAA,GAAwDlD,QAAQ,CAA+B,IAAI,CAAC;IAAAmD,WAAA,GAAA5B,cAAA,CAAA2B,WAAA;IAA7FE,oBAAoB,GAAAD,WAAA;IAAEE,uBAAuB,GAAAF,WAAA;EAEpDlD,SAAS,CAAC,YAAM;IAEd,IAAMqD,SAAS,GAAGzC,EAAE,CAACM,UAAU,CAAC;IAChCkB,SAAS,CAACiB,SAAS,CAAC;IAEpBA,SAAS,CAACC,EAAE,CAAC,SAAS,EAAE,YAAM;MAC5BC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,SAAS,CAACI,EAAE,CAAC;IACnD,CAAC,CAAC;IAEFJ,SAAS,CAACC,EAAE,CAAC,YAAY,EAAE,YAAM;MAC/BC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC;IAEFH,SAAS,CAACC,EAAE,CAAC,WAAW,EAAE,YAAM;MAC9BC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACtC,CAAC,CAAC;IAEFH,SAAS,CAACC,EAAE,CAAC,YAAY,EAAE,UAACI,IAAS,EAAK;MACxCH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEE,IAAI,CAAC;MAG1C,IAAIA,IAAI,CAACC,OAAO,EAAE;QAEhB,IAAID,IAAI,CAACC,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;UAElChB,aAAa,CAAC,MAAM,CAAC;UAGrB,IAAMiB,aAAa,GAAGH,IAAI,CAACC,OAAO,CAACG,OAAO,CAACC,IAAI;UAC/CC,UAAU,CAAC,YAAM;YACf3D,KAAK,CAAC4D,KAAK,CACT,sBAAsB,EACtBJ,aAAa,EACb,CAAC;cAAEE,IAAI,EAAE,IAAI;cAAEG,KAAK,EAAE;YAAU,CAAC,CACnC,CAAC;UACH,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAGI,IAAIR,IAAI,CAACC,OAAO,CAACC,IAAI,KAAK,cAAc,EAAE;UAE7C,IAAMO,MAAM,GAAGT,IAAI,CAACC,OAAO,CAACG,OAAO,CAACC,IAAI,IAAI,EAAE;UAG9C,IAAI,6CAA6C,CAACK,IAAI,CAACD,MAAM,CAAC,EAAE;YAC9DvB,aAAa,CAAC,WAAW,CAAC;YAC1BI,UAAU,CAAC,WAAW,CAAC;UACzB,CAAC,MAAM,IAAI,4CAA4C,CAACoB,IAAI,CAACD,MAAM,CAAC,EAAE;YACpEnB,UAAU,CAAC,UAAU,CAAC;UACxB,CAAC,MAAM,IAAI,oCAAoC,CAACoB,IAAI,CAACD,MAAM,CAAC,EAAE;YAC5DnB,UAAU,CAAC,WAAW,CAAC;UACzB,CAAC,MAAM,IAAI,sCAAsC,CAACoB,IAAI,CAACD,MAAM,CAAC,EAAE;YAC9DnB,UAAU,CAAC,UAAU,CAAC;UACxB,CAAC,MAAM;YAELA,UAAU,CAAC,SAAS,CAAC;UACvB;QACF,CAAC,MAGI,IAAIU,IAAI,CAACC,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;UACvCL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,IAAI,CAACC,OAAO,CAACG,OAAO,CAAC;QACtD;MACF;MAGA,IAAIJ,IAAI,CAACW,aAAa,EAAE;QACtBd,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEE,IAAI,CAACW,aAAa,CAAC;MAC5D;IACF,CAAC,CAAC;IAEFhB,SAAS,CAACC,EAAE,CAAC,eAAe;MAAA,IAAAgB,IAAA,GAAAC,iBAAA,CAAE,WAAOb,IAAS,EAAK;QACjDH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEE,IAAI,CAAC;QAG7C,IAAIA,IAAI,CAACc,QAAQ,EAAE;UACjB,IAAI;YAEF5B,aAAa,CAAC,UAAU,CAAC;YAGzB,IAAA6B,qBAAA,SAAwB/D,KAAK,CAACgE,KAAK,CAACC,WAAW,CAC7C;gBAAEC,GAAG,EAAE,GAAG1D,UAAU,GAAGwC,IAAI,CAACc,QAAQ;cAAG,CAAC,EACxC;gBAAEK,UAAU,EAAE;cAAK,CACrB,CAAC;cAHOC,KAAK,GAAAL,qBAAA,CAALK,KAAK;YAMbA,KAAK,CAACC,yBAAyB,CAAC,UAACC,MAAM,EAAK;cAC1C,IAAIA,MAAM,CAACC,QAAQ,IAAI,CAACD,MAAM,CAACE,WAAW,IAAIF,MAAM,CAACG,aAAa,EAAE;gBAElEvC,aAAa,CAAC,MAAM,CAAC;gBACrBkC,KAAK,CAACM,WAAW,CAAC,CAAC;cACrB;YACF,CAAC,CAAC;UACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;YACd9B,OAAO,CAAC8B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;YACrDzC,aAAa,CAAC,MAAM,CAAC;UACvB;QACF;MACF,CAAC;MAAA,iBAAA0C,EAAA;QAAA,OAAAhB,IAAA,CAAAiB,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;IAGFnC,SAAS,CAACC,EAAE,CAAC,aAAa,EAAE,UAACI,IAAS,EAAK;MACzCH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEE,IAAI,CAAC;MAE3C,IAAIA,IAAI,CAAC+B,KAAK,KAAK,KAAK,IAAI/B,IAAI,CAAC+B,KAAK,KAAK,QAAQ,EAAE;QACnD7C,aAAa,CAAC,WAAW,CAAC;QAC1BI,UAAU,CAAC,WAAW,CAAC;QAGvB3C,KAAK,CAAC4D,KAAK,CACT,kBAAkB,EAClBP,IAAI,CAACC,OAAO,IAAI,yBAAyB,EACzC,CAAC;UAAEI,IAAI,EAAE,aAAa;UAAEG,KAAK,EAAE;QAAc,CAAC,CAChD,CAAC;MACH;IACF,CAAC,CAAC;IAEF,OAAO;MAAA,OAAMb,SAAS,CAACqC,KAAK,CAAC,CAAC;IAAA;EAChC,CAAC,EAAE,CAACnE,SAAS,CAAC,CAAC;EAEf,IAAMoE,cAAc;IAAA,IAAAC,KAAA,GAAArB,iBAAA,CAAG,aAAY;MACjC,IAAI;QACF,IAAMsB,UAAU,SAASnF,KAAK,CAACoF,uBAAuB,CAAC,CAAC;QACxD,IAAID,UAAU,CAACb,MAAM,KAAK,SAAS,EAAE;UACnC3E,KAAK,CAAC4D,KAAK,CAAC,qBAAqB,EAAE,oCAAoC,CAAC;UACxE;QACF;QAEA,MAAMvD,KAAK,CAACqF,iBAAiB,CAAC;UAC5BC,kBAAkB,EAAE,IAAI;UACxBC,oBAAoB,EAAE;QACxB,CAAC,CAAC;QAEFzE,YAAY,CAAC,WAAW,CAAC;QACzBoB,aAAa,CAAC,WAAW,CAAC;QAC1B,IAAAsD,qBAAA,SAA4BxF,KAAK,CAACyF,SAAS,CAACxB,WAAW,CACrDjE,KAAK,CAAC0F,uBAAuB,CAACC,YAAY,EAC1C,UAACrB,MAAM,EAAK;YACV5B,uBAAuB,CAAC4B,MAAM,CAAC;YAE/B,IAAIA,MAAM,CAACsB,QAAQ,IAAItB,MAAM,CAACsB,QAAQ,GAAG,CAAC,EAAE,EAAE;cAE5C,IAAMb,KAAK,GAAGc,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACxB,MAAM,CAACsB,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC;cACrD9D,aAAa,CAACiD,KAAK,CAAC;YACtB;UACF,CAAC,EACD,GACF,CAAC;UAZO9D,UAAS,GAAAuE,qBAAA,CAATvE,SAAS;QAajBC,YAAY,CAACD,UAAS,CAAC;QAGvBqC,UAAU,CAAC,YAAM;UACf,IAAIrC,UAAS,EAAE;YACb8E,aAAa,CAAC,CAAC;UACjB;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZnD,OAAO,CAAC8B,KAAK,CAAC,2BAA2B,EAAEqB,GAAG,CAAC;QAC/ClF,YAAY,CAAC,MAAM,CAAC;QACpBoB,aAAa,CAAC,MAAM,CAAC;MACvB;IACF,CAAC;IAAA,gBAzCK+C,cAAcA,CAAA;MAAA,OAAAC,KAAA,CAAAL,KAAA,OAAAC,SAAA;IAAA;EAAA,GAyCnB;EAED,IAAMiB,aAAa;IAAA,IAAAE,KAAA,GAAApC,iBAAA,CAAG,aAAY;MAChC,IAAI,CAAC5C,SAAS,EAAE;MAEhBH,YAAY,CAAC,UAAU,CAAC;MACxBoB,aAAa,CAAC,UAAU,CAAC;MACzBJ,aAAa,CAAC,CAAC,CAAC;MAChBZ,YAAY,CAAC,IAAI,CAAC;MAElB,IAAI;QACF,MAAMD,SAAS,CAACiF,kBAAkB,CAAC,CAAC;QACpC,IAAMhC,GAAG,GAAGjD,SAAS,CAACkF,MAAM,CAAC,CAAC;QAE9B,IAAIjC,GAAG,EAAE;UACP,MAAMkC,WAAW,CAAClC,GAAG,CAAC;QACxB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACd9B,OAAO,CAAC8B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD7D,YAAY,CAAC,MAAM,CAAC;QACpBoB,aAAa,CAAC,MAAM,CAAC;MACvB;IACF,CAAC;IAAA,gBApBK6D,aAAaA,CAAA;MAAA,OAAAE,KAAA,CAAApB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAoBlB;EAED,IAAMsB,WAAW;IAAA,IAAAC,KAAA,GAAAxC,iBAAA,CAAG,WAAOK,GAAW,EAAK;MACzC,IAAI;QAEF,IAAMoC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE;UACvBtC,GAAG,EAAHA,GAAG;UACHuC,IAAI,EAAE,WAAW;UACjBC,IAAI,EAAE;QACR,CAAQ,CAAC;QAET,IAAMC,WAAW,SAASC,KAAK,CAAC,GAAGpG,UAAU,aAAa,EAAE;UAC1DqG,MAAM,EAAE,MAAM;UACdC,IAAI,EAAER,QAAQ;UACdS,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIC,aAAa,GAAG,EAAE;QAEtB,IAAIL,WAAW,CAACM,EAAE,EAAE;UAClB,IAAMC,SAAS,SAASP,WAAW,CAACQ,IAAI,CAAC,CAAC;UAC1CH,aAAa,GAAGE,SAAS,CAACF,aAAa,IAAI,4BAA4B;UACvEnE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoE,SAAS,CAAC;QACjD,CAAC,MAAM;UAEL,IAAME,kBAAkB,GAAG,CACzB,mEAAmE,EACnE,iDAAiD,EACjD,mDAAmD,EACnD,kDAAkD,EAClD,sCAAsC,CACvC;UACDJ,aAAa,GAAGI,kBAAkB,CAACvB,IAAI,CAACwB,KAAK,CAACxB,IAAI,CAACyB,MAAM,CAAC,CAAC,GAAGF,kBAAkB,CAACG,MAAM,CAAC,CAAC;UACzF1E,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC5D;QAGA,IAAM0E,QAAQ,SAASZ,KAAK,CAAC,GAAGpG,UAAU,+BAA+B,EAAE;UACzEqG,MAAM,EAAE,MAAM;UACdE,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDD,IAAI,EAAEW,IAAI,CAACC,SAAS,CAAC;YACnBzE,OAAO,EAAE+D;UACX,CAAC;QACH,CAAC,CAAC;QAEF,IAAIQ,QAAQ,CAACP,EAAE,EAAE;UACf,IAAMU,MAAM,SAASH,QAAQ,CAACL,IAAI,CAAC,CAAC;UACpCtE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6E,MAAM,CAAC;UAG/C,IAAMC,cAAc,GAAGD,MAAM,CAACE,UAAU,CAACzE,OAAO,CAACC,IAAI;UAErD,IAAI;YACF,IAAMyE,WAAW,SAASlB,KAAK,CAAC,GAAGpG,UAAU,aAAa,EAAE;cAC1DqG,MAAM,EAAE,MAAM;cACdE,OAAO,EAAE;gBACP,cAAc,EAAE;cAClB,CAAC;cACDD,IAAI,EAAEW,IAAI,CAACC,SAAS,CAAC;gBACnBrE,IAAI,EAAEuE,cAAc;gBACpBG,KAAK,EAAE,sBAAsB;gBAC7BC,SAAS,EAAE;cACb,CAAC;YACH,CAAC,CAAC;YAEF,IAAIF,WAAW,CAACb,EAAE,EAAE;cAClB,IAAMgB,SAAS,SAASH,WAAW,CAACX,IAAI,CAAC,CAAC;cAC1CtE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmF,SAAS,CAACnE,QAAQ,CAAC;cAGvD,IAAI;gBACF,IAAAoE,sBAAA,SAAwBlI,KAAK,CAACgE,KAAK,CAACC,WAAW,CAC7C;oBAAEC,GAAG,EAAE,GAAG1D,UAAU,GAAGyH,SAAS,CAACnE,QAAQ;kBAAG,CAAC,EAC7C;oBAAEK,UAAU,EAAE;kBAAK,CACrB,CAAC;kBAHOC,KAAK,GAAA8D,sBAAA,CAAL9D,KAAK;gBAMblC,aAAa,CAAC,UAAU,CAAC;gBAGzBkC,KAAK,CAACC,yBAAyB,CAAC,UAACC,MAAM,EAAK;kBAC1C,IAAIA,MAAM,CAACC,QAAQ,IAAI,CAACD,MAAM,CAACE,WAAW,IAAIF,MAAM,CAACG,aAAa,EAAE;oBAElEvC,aAAa,CAAC,MAAM,CAAC;oBACrBI,UAAU,CAAC,SAAS,CAAC;oBACrB8B,KAAK,CAACM,WAAW,CAAC,CAAC;kBACrB;gBACF,CAAC,CAAC;gBAGF/E,KAAK,CAAC4D,KAAK,CACT,4BAA4B,EAC5B,cAAcyD,aAAa,GAAG,EAC9B,CAAC;kBAAE3D,IAAI,EAAE,IAAI;kBAAEG,KAAK,EAAE;gBAAU,CAAC,CACnC,CAAC;cACH,CAAC,CAAC,OAAO2E,SAAS,EAAE;gBAClBtF,OAAO,CAAC8B,KAAK,CAAC,uBAAuB,EAAEwD,SAAS,CAAC;gBACjDxI,KAAK,CAAC4D,KAAK,CACT,4BAA4B,EAC5B,cAAcyD,aAAa,wBAAwBY,cAAc,mCAAmC,EACpG,CAAC;kBAAEvE,IAAI,EAAE,IAAI;kBAAEG,KAAK,EAAE;gBAAU,CAAC,CACnC,CAAC;cACH;YACF,CAAC,MAAM;cAELF,UAAU,CAAC,YAAM;gBACf3D,KAAK,CAAC4D,KAAK,CACT,4BAA4B,EAC5B,cAAcyD,aAAa,wBAAwBY,cAAc,EAAE,EACnE,CAAC;kBAAEvE,IAAI,EAAE,IAAI;kBAAEG,KAAK,EAAE;gBAAU,CAAC,CACnC,CAAC;cACH,CAAC,EAAE,IAAI,CAAC;YACV;UACF,CAAC,CAAC,OAAO4E,QAAQ,EAAE;YACjBvF,OAAO,CAAC8B,KAAK,CAAC,YAAY,EAAEyD,QAAQ,CAAC;YAErC9E,UAAU,CAAC,YAAM;cACf3D,KAAK,CAAC4D,KAAK,CACT,4BAA4B,EAC5B,cAAcyD,aAAa,wBAAwBY,cAAc,EAAE,EACnE,CAAC;gBAAEvE,IAAI,EAAE,IAAI;gBAAEG,KAAK,EAAE;cAAU,CAAC,CACnC,CAAC;YACH,CAAC,EAAE,IAAI,CAAC;UACV;QACF,CAAC,MAAM;UACL,MAAM,IAAI6E,KAAK,CAAC,eAAe,CAAC;QAClC;MACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;QACd9B,OAAO,CAAC8B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3ChF,KAAK,CAAC4D,KAAK,CAAC,OAAO,EAAE,iCAAiC,CAAC;MACzD,CAAC,SAAS;QACRzC,YAAY,CAAC,MAAM,CAAC;MACtB;IACF,CAAC;IAAA,gBAxIKsF,WAAWA,CAAAkC,GAAA;MAAA,OAAAjC,KAAA,CAAAxB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAwIhB;EAED,IAAMyD,SAAS;IAAA,IAAAC,KAAA,GAAA3E,iBAAA,CAAG,aAAY;MAC5B,IAAM8D,MAAM,SAAS1H,WAAW,CAACwI,uBAAuB,CAAC;QACvDC,UAAU,EAAEzI,WAAW,CAAC0I,gBAAgB,CAACC,MAAM;QAC/CC,aAAa,EAAE,IAAI;QACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,IAAI,CAACpB,MAAM,CAACqB,QAAQ,IAAIrB,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAE;QACxCnI,YAAY,CAAC,UAAU,CAAC;QACxB,MAAMoI,WAAW,CAACvB,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,CAAC/E,GAAG,CAAC;MACzC;IACF,CAAC;IAAA,gBAZKqE,SAASA,CAAA;MAAA,OAAAC,KAAA,CAAA3D,KAAA,OAAAC,SAAA;IAAA;EAAA,GAYd;EAED,IAAMoE,WAAW;IAAA,IAAAC,KAAA,GAAAtF,iBAAA,CAAG,WAAOK,GAAW,EAAK;MACzC,IAAI;QACF,IAAMoC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE;UACtBtC,GAAG,EAAHA,GAAG;UACHuC,IAAI,EAAE,YAAY;UAClBC,IAAI,EAAE;QACR,CAAQ,CAAC;QAET,IAAMc,QAAQ,SAASZ,KAAK,CAAC,GAAGpG,UAAU,SAAS,EAAE;UACnDqG,MAAM,EAAE,MAAM;UACdC,IAAI,EAAER,QAAQ;UACdS,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIS,QAAQ,CAACP,EAAE,EAAE;UACf,IAAMU,MAAM,SAASH,QAAQ,CAACL,IAAI,CAAC,CAAC;UACpCtE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE6E,MAAM,CAAC;QACrD,CAAC,MAAM;UACL,MAAM,IAAIU,KAAK,CAAC,eAAe,CAAC;QAClC;MACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;QACd9B,OAAO,CAAC8B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3ChF,KAAK,CAAC4D,KAAK,CAAC,OAAO,EAAE,wBAAwB,CAAC;MAChD,CAAC,SAAS;QACRzC,YAAY,CAAC,MAAM,CAAC;MACtB;IACF,CAAC;IAAA,gBA7BKoI,WAAWA,CAAAE,GAAA;MAAA,OAAAD,KAAA,CAAAtE,KAAA,OAAAC,SAAA;IAAA;EAAA,GA6BhB;EAED,IAAMuE,eAAe;IAAA,IAAAC,KAAA,GAAAzF,iBAAA,CAAG,aAAY;MAClC,IAAI,CAACxC,SAAS,CAACkI,IAAI,CAAC,CAAC,EAAE;MAEvBzI,YAAY,CAAC,UAAU,CAAC;MAExB,IAAI;QAEF,IAAM0G,QAAQ,SAASZ,KAAK,CAAC,GAAGpG,UAAU,+BAA+B,EAAE;UACzEqG,MAAM,EAAE,MAAM;UACdE,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDD,IAAI,EAAEW,IAAI,CAACC,SAAS,CAAC;YACnBzE,OAAO,EAAE5B,SAAS,CAACkI,IAAI,CAAC;UAC1B,CAAC;QACH,CAAC,CAAC;QAEF,IAAI/B,QAAQ,CAACP,EAAE,EAAE;UACf,IAAMU,MAAM,SAASH,QAAQ,CAACL,IAAI,CAAC,CAAC;UACpCtE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6E,MAAM,CAACE,UAAU,CAACzE,OAAO,CAACC,IAAI,CAAC;UACpE/B,YAAY,CAAC,EAAE,CAAC;UAGhBgC,UAAU,CAAC,YAAM;YACf3D,KAAK,CAAC4D,KAAK,CACT,0BAA0B,EAC1BoE,MAAM,CAACE,UAAU,CAACzE,OAAO,CAACC,IAAI,EAC9B,CAAC;cAAEA,IAAI,EAAE,IAAI;cAAEG,KAAK,EAAE;YAAU,CAAC,CACnC,CAAC;UACH,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,MAAM,IAAI6E,KAAK,CAAC,aAAa,CAAC;QAChC;MACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;QACd9B,OAAO,CAAC8B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3ChF,KAAK,CAAC4D,KAAK,CAAC,OAAO,EAAE,wBAAwB,CAAC;MAChD,CAAC,SAAS;QACRzC,YAAY,CAAC,MAAM,CAAC;MACtB;IACF,CAAC;IAAA,gBAvCKuI,eAAeA,CAAA;MAAA,OAAAC,KAAA,CAAAzE,KAAA,OAAAC,SAAA;IAAA;EAAA,GAuCpB;EAED,OACEvE,KAAA,CAACV,oBAAoB;IACnB2D,KAAK,EAAEgG,MAAM,CAACC,SAAU;IACxBC,QAAQ,EAAE5J,QAAQ,CAAC6J,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG,QAAS;IAAAC,QAAA,GAEvDrJ,KAAA,CAAChB,IAAI;MAACiE,KAAK,EAAEgG,MAAM,CAACK,MAAO;MAAAD,QAAA,GACzBrJ,KAAA,CAAChB,IAAI;QAACiE,KAAK,EAAEgG,MAAM,CAACM,aAAc;QAAAF,QAAA,GAChCvJ,IAAA,CAACb,IAAI;UAACgE,KAAK,EAAEgG,MAAM,CAACO,QAAS;UAAAH,QAAA,EAAC;QAAE,CAAM,CAAC,EACvCvJ,IAAA,CAACb,IAAI;UAACgE,KAAK,EAAEgG,MAAM,CAACQ,KAAM;UAAAJ,QAAA,EAAC;QAAM,CAAM,CAAC;MAAA,CACpC,CAAC,EACPvJ,IAAA,CAACb,IAAI;QAACgE,KAAK,EAAEgG,MAAM,CAACS,QAAS;QAAAL,QAAA,EAAC;MAAwB,CAAM,CAAC;IAAA,CACzD,CAAC,EAEPrJ,KAAA,CAAChB,IAAI;MAACiE,KAAK,EAAEgG,MAAM,CAACU,aAAc;MAAAN,QAAA,GAChCvJ,IAAA,CAACF,qBAAqB;QACpB8B,UAAU,EAAEA,UAAW;QACvBJ,UAAU,EAAEA,UAAW;QACvBsI,WAAW,EAAEtJ,SAAS,KAAK,WAAY;QACvCuJ,UAAU,EAAEvJ,SAAS,KAAK,UAAW;QACrCwJ,UAAU,EAAEpI,UAAU,KAAK,UAAW;QACtCqI,WAAW,EAAErI,UAAU,KAAK,WAAY;QACxCI,OAAO,EAAEA;MAAQ,CAClB,CAAC,EAEF9B,KAAA,CAACf,IAAI;QAACgE,KAAK,EAAEgG,MAAM,CAACe,SAAU;QAAAX,QAAA,GAC3B3H,UAAU,KAAK,MAAM,IAAI,kCAAkC,EAC3DA,UAAU,KAAK,WAAW,IAAI,2BAA2B,EACzDA,UAAU,KAAK,UAAU,IAAI,wBAAwB,EACrDA,UAAU,KAAK,UAAU,IAAI,yBAAyB,EACtDA,UAAU,KAAK,WAAW,IAAI,qCAAqC;MAAA,CAChE,CAAC;IAAA,CACH,CAAC,EAEP1B,KAAA,CAAChB,IAAI;MAACiE,KAAK,EAAEgG,MAAM,CAACgB,QAAS;MAAAZ,QAAA,GAC3BvJ,IAAA,CAACZ,gBAAgB;QACf+D,KAAK,EAAE,CAACgG,MAAM,CAACiB,SAAS,EAAE5J,SAAS,KAAK,WAAW,IAAI2I,MAAM,CAACkB,eAAe,CAAE;QAC/EC,OAAO,EAAE9J,SAAS,KAAK,WAAW,GAAGkF,aAAa,GAAGd,cAAe;QACpE2F,QAAQ,EAAE/J,SAAS,KAAK,UAAW;QAAA+I,QAAA,EAEnCvJ,IAAA,CAACN,QAAQ;UACP2G,IAAI,EAAE7F,SAAS,KAAK,WAAW,GAAG,MAAM,GAAG,KAAM;UACjDgK,IAAI,EAAE,EAAG;UACTC,KAAK,EAAC;QAAO,CACd;MAAC,CACc,CAAC,EAEnBzK,IAAA,CAACZ,gBAAgB;QACf+D,KAAK,EAAEgG,MAAM,CAACuB,WAAY;QAC1BJ,OAAO,EAAEpC,SAAU;QACnBqC,QAAQ,EAAE/J,SAAS,KAAK,MAAO;QAAA+I,QAAA,EAE/BvJ,IAAA,CAACN,QAAQ;UAAC2G,IAAI,EAAC,QAAQ;UAACmE,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS,CAAE;MAAC,CACpC,CAAC;IAAA,CACf,CAAC,EAEPvK,KAAA,CAAChB,IAAI;MAACiE,KAAK,EAAEgG,MAAM,CAACwB,kBAAmB;MAAApB,QAAA,GACrCvJ,IAAA,CAACT,SAAS;QACR4D,KAAK,EAAEgG,MAAM,CAACnI,SAAU;QACxB4J,WAAW,EAAC,uCAAuC;QACnDC,oBAAoB,EAAC,SAAS;QAC9BC,KAAK,EAAE9J,SAAU;QACjB+J,YAAY,EAAE9J,YAAa;QAC3B+J,SAAS;MAAA,CACV,CAAC,EACFhL,IAAA,CAACZ,gBAAgB;QACf+D,KAAK,EAAE,CAACgG,MAAM,CAAC8B,UAAU,EAAE,CAACjK,SAAS,CAACkI,IAAI,CAAC,CAAC,IAAIC,MAAM,CAAC+B,kBAAkB,CAAE;QAC3EZ,OAAO,EAAEtB,eAAgB;QACzBuB,QAAQ,EAAE,CAACvJ,SAAS,CAACkI,IAAI,CAAC,CAAE;QAAAK,QAAA,EAE5BvJ,IAAA,CAACN,QAAQ;UAAC2G,IAAI,EAAC,MAAM;UAACmE,IAAI,EAAE,EAAG;UAACC,KAAK,EAAEzJ,SAAS,CAACkI,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG;QAAU,CAAE;MAAC,CACnE,CAAC;IAAA,CACf,CAAC;EAAA,CACa,CAAC;AAE3B,CAAC;AAED,IAAMC,MAAM,GAAG9J,UAAU,CAAC8L,MAAM,CAAC;EAC/B/B,SAAS,EAAE;IACTgC,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BC,UAAU,EAAE;EACd,CAAC;EACD9B,MAAM,EAAE;IACN+B,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE,EAAE;IACnBL,eAAe,EAAE,uBAAuB;IACxCM,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE,EAAE;IACpBC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACD5C,aAAa,EAAE;IACb6C,aAAa,EAAE,KAAK;IACpBf,UAAU,EAAE,QAAQ;IACpBgB,cAAc,EAAE,QAAQ;IACxBd,YAAY,EAAE;EAChB,CAAC;EACD/B,QAAQ,EAAE;IACR8C,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC;EACD9C,KAAK,EAAE;IACL6C,QAAQ,EAAE,EAAE;IACZE,UAAU,EAAE,KAAK;IACjBjC,KAAK,EAAE,SAAS;IAChBkC,SAAS,EAAE;EACb,CAAC;EACD/C,QAAQ,EAAE;IACR4C,QAAQ,EAAE,EAAE;IACZ/B,KAAK,EAAE,SAAS;IAChBkC,SAAS,EAAE,QAAQ;IACnBD,UAAU,EAAE;EACd,CAAC;EACD7C,aAAa,EAAE;IACbuB,IAAI,EAAE,CAAC;IACPG,UAAU,EAAE,QAAQ;IACpBgB,cAAc,EAAE,QAAQ;IACxBf,iBAAiB,EAAE;EACrB,CAAC;EACDtB,SAAS,EAAE;IACTsC,QAAQ,EAAE,EAAE;IACZ/B,KAAK,EAAE,SAAS;IAChBkC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE;EACb,CAAC;EACDzC,QAAQ,EAAE;IACRmC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,QAAQ;IACxBhB,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,YAAY,EAAE,EAAE;IAChBoB,GAAG,EAAE;EACP,CAAC;EACDzC,SAAS,EAAE;IACT2B,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVL,YAAY,EAAE,EAAE;IAChBN,eAAe,EAAE,SAAS;IAC1BE,UAAU,EAAE,QAAQ;IACpBgB,cAAc,EAAE,QAAQ;IACxBV,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACD9B,eAAe,EAAE;IACfgB,eAAe,EAAE,SAAS;IAC1BQ,WAAW,EAAE;EACf,CAAC;EACDnB,WAAW,EAAE;IACXqB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVL,YAAY,EAAE,EAAE;IAChBN,eAAe,EAAE,SAAS;IAC1BE,UAAU,EAAE,QAAQ;IACpBgB,cAAc,EAAE,QAAQ;IACxBH,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACD1B,kBAAkB,EAAE;IAClB2B,aAAa,EAAE,KAAK;IACpBf,UAAU,EAAE,UAAU;IACtBC,iBAAiB,EAAE,EAAE;IACrBsB,aAAa,EAAE,EAAE;IACjBD,GAAG,EAAE;EACP,CAAC;EACD7L,SAAS,EAAE;IACToK,IAAI,EAAE,CAAC;IACPgB,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,SAAS;IACtBV,YAAY,EAAE,EAAE;IAChBH,iBAAiB,EAAE,EAAE;IACrBE,eAAe,EAAE,EAAE;IACnBL,eAAe,EAAE,SAAS;IAC1BZ,KAAK,EAAE,SAAS;IAChBsC,SAAS,EAAE,GAAG;IACdP,QAAQ,EAAE;EACZ,CAAC;EACDvB,UAAU,EAAE;IACVc,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVL,YAAY,EAAE,EAAE;IAChBJ,UAAU,EAAE,QAAQ;IACpBgB,cAAc,EAAE;EAClB,CAAC;EACDrB,kBAAkB,EAAE;IAClB8B,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAe5M,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}