import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import Agent from './screens/Agent';
import Inbox from './screens/Inbox';
import TabBar from './components/TabBar';

export default function App() {
  const [activeTab, setActiveTab] = useState('agent');

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {activeTab === 'agent' ? (
        <Agent />
      ) : (
        <Inbox />
      )}
      
      <TabBar activeTab={activeTab} onTabChange={setActiveTab} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#111827',
  },
});
